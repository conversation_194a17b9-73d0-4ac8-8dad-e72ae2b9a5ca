import { <PERSON><PERSON> } from 'eslint';
import typescriptPlugin from '@typescript-eslint/eslint-plugin';
import typescriptParser from '@typescript-eslint/parser';
import prettierPlugin from 'eslint-plugin-prettier';

const config: Linter.FlatConfig[] = [
  {
    files: ['**/*.ts'],
    languageOptions: {
      parser: typescriptParser,
      parserOptions: {
        ecmaVersion: 2020, // Allows parsing of modern ECMAScript features
        sourceType: 'module', // Allows the use of imports
      },
    },
    plugins: {
      '@typescript-eslint': typescriptPlugin,
      prettier: prettierPlugin,
    },
    rules: {
      ...typescriptPlugin.configs.recommended.rules, // Use recommended TypeScript rules
      ...(prettierPlugin.configs?.recommended?.rules ?? {}), // Use Prettier rules
      '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }], // Ignore unused variables starting with "_"
      '@typescript-eslint/explicit-function-return-type': 'off', // Disable explicit return types for functions
      '@typescript-eslint/no-explicit-any': 'warn', // Warn when using "any" type
      'prettier/prettier': 'error', // Treat Prettier issues as errors
    },
  },
  {
    ignores: ['node_modules', 'dist', '.github'], // Ignore these directories
  },
];

export default config;