generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String         @id @default(uuid())
  name         String
  email        String         @unique
  password     String
  families     FamilyMember[]
  expenses     Expense[]
  accounts     Account[]
  createdAt    DateTime       @default(now())
  Income       Income[]
  Notification Notification[]
}

model Family {
  id        String         @id @default(uuid())
  name      String
  members   FamilyMember[]
  expenses  Expense[]
  accounts  Account[]
  createdAt DateTime       @default(now())
  Income    Income[]
}

model FamilyMember {
  id       String @id @default(uuid())
  userId   String
  familyId String
  role     String
  user     User   @relation(fields: [userId], references: [id])
  family   Family @relation(fields: [familyId], references: [id])
}

model Account {
  id         String      @id @default(uuid())
  name       String
  type       AccountType
  bank       String
  userId     String
  familyId   String?
  closingDay Int?
  dueDay     Int?
  createdAt  DateTime    @default(now())

  user     User      @relation(fields: [userId], references: [id])
  family   Family?   @relation(fields: [familyId], references: [id])
  expenses Expense[]
  Income   Income[]
}

enum AccountType {
  CREDIT
  DEBIT
  CHECKING
  SAVINGS
}

model Expense {
  id              String           @id @default(uuid())
  userId          String
  familyId        String?
  accountId       String
  title           String
  amount          Float
  category        String
  date            DateTime
  isInstallment   Boolean          @default(false)
  installmentInfo InstallmentInfo?
  notes           String?
  createdAt       DateTime         @default(now())

  user    User    @relation(fields: [userId], references: [id])
  family  Family? @relation(fields: [familyId], references: [id])
  account Account @relation(fields: [accountId], references: [id])
}

model InstallmentInfo {
  id        String   @id @default(uuid())
  expenseId String   @unique
  total     Int
  current   Int
  startDate DateTime
  endDate   DateTime

  expense Expense @relation(fields: [expenseId], references: [id])
}

model Income {
  id          String   @id @default(uuid())
  userId      String
  familyId    String?
  accountId   String
  title       String
  amount      Float
  category    String
  date        DateTime
  isRecurring Boolean  @default(false)
  frequency   String? // monthly, weekly, etc.
  notes       String?
  createdAt   DateTime @default(now())

  user    User    @relation(fields: [userId], references: [id])
  family  Family? @relation(fields: [familyId], references: [id])
  account Account @relation(fields: [accountId], references: [id])
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  type      String // EXPENSE_DUE, BALANCE_LOW, BUDGET_EXCEEDED
  message   String
  read      Boolean  @default(false)
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}
