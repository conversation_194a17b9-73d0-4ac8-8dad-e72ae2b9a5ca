import { PrismaClient } from '@prisma/client';

// Ensure we're using the test database
const testDatabaseUrl = 'file:./test.db';

// Create a test-specific Prisma instance
export const testPrisma = new PrismaClient({
  datasources: {
    db: {
      url: testDatabaseUrl,
    },
  },
  log: ['error'],
});

export async function cleanDatabase() {
  // Clean tables in the right order to respect foreign key constraints
  await testPrisma.installmentInfo.deleteMany();
  await testPrisma.expense.deleteMany();
  await testPrisma.account.deleteMany();
  await testPrisma.user.deleteMany();
}

export async function setupTestDatabase() {
  await testPrisma.$connect();
}

export async function teardownTestDatabase() {
  await cleanDatabase();
  await testPrisma.$disconnect();
}
