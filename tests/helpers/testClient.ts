import request from 'supertest';
import { Express } from 'express';
import setupExpress from '../../src/config/express';
import setupRoutes from '../../src/config/routes';

export interface TestUser {
  id: string;
  name: string;
  email: string;
  token: string;
}

export interface TestAccount {
  id: string;
  name: string;
  type: string;
  bank: string;
  userId: string;
}

export interface TestExpense {
  id: string;
  title: string;
  amount: number;
  date: string;
  accountId: string;
}

export class TestClient {
  public app: Express;

  constructor() {
    this.app = setupExpress();
    setupRoutes(this.app);
  }

  async registerUser(
    userData = {
      name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
    },
  ): Promise<TestUser> {
    const response = await request(this.app)
      .post('/auth/register')
      .send(userData)
      .expect(201);

    const loginResponse = await request(this.app)
      .post('/auth/login')
      .send({
        email: userData.email,
        password: userData.password,
      })
      .expect(200);

    return {
      id: response.body.data.user.id,
      name: response.body.data.user.name,
      email: response.body.data.user.email,
      token: loginResponse.body.data.token,
    };
  }

  async createAccount(
    user: TestUser,
    accountData = {
      name: 'Test Account',
      type: 'CHECKING',
      bank: 'Test Bank',
    },
  ): Promise<TestAccount> {
    const response = await request(this.app)
      .post('/accounts')
      .set('Authorization', `Bearer ${user.token}`)
      .send(accountData)
      .expect(201);

    return response.body.data.account;
  }

  async createExpense(
    user: TestUser,
    expenseData: {
      title: string;
      amount: number;
      date: string;
      accountId: string;
      category?: string;
      familyId?: string | null;
    },
  ): Promise<TestExpense> {
    const requestData = {
      ...expenseData,
      category: expenseData.category || 'General',
    };

    const response = await request(this.app)
      .post('/expenses')
      .set('Authorization', `Bearer ${user.token}`)
      .send(requestData)
      .expect(201);

    return response.body.data.data;
  }

  request() {
    return request(this.app);
  }

  withAuth(token: string) {
    const req = request(this.app);
    return {
      get: (url: string) => req.get(url).set('Authorization', `Bearer ${token}`),
      post: (url: string) => req.post(url).set('Authorization', `Bearer ${token}`),
      put: (url: string) => req.put(url).set('Authorization', `Bearer ${token}`),
      delete: (url: string) => req.delete(url).set('Authorization', `Bearer ${token}`),
    };
  }
}
