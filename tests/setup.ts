import { setupTestDatabase, teardownTestDatabase, cleanDatabase } from './helpers/database';
import { config } from 'dotenv';

// Load test environment variables first
config({ path: '.env.test' });

// Ensure the DATABASE_URL is set for tests
process.env.DATABASE_URL = 'file:./test.db';
process.env.NODE_ENV = 'test';

beforeAll(async () => {
  // Connect to test database
  await setupTestDatabase();
});

afterAll(async () => {
  // Clean up and disconnect
  await teardownTestDatabase();
});

beforeEach(async () => {
  // Clean database before each test
  await cleanDatabase();
});

// Global test timeout (30 seconds to match Jest config)
jest.setTimeout(30000);
