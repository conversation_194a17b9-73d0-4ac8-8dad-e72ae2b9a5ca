import { TestClient, TestUser, TestAccount } from '../helpers/testClient';

describe('Expenses Integration Tests', () => {
  let client: TestClient;
  let user: TestUser;
  let account: TestAccount;

  beforeEach(async () => {
    client = new TestClient();
    user = await client.registerUser();
    account = await client.createAccount(user);
  });

  describe('POST /expenses', () => {
    it('should create a new expense successfully', async () => {
      const expenseData = {
        title: 'Grocery shopping',
        amount: 50.75,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
        category: 'Food',
      };

      const response = await client
        .request()
        .post('/expenses')
        .set('Authorization', `Bearer ${user.token}`)
        .send(expenseData)
        .expect(201);

      expect(response.body.data.data).toHaveProperty('id');
      expect(response.body.data.data.title).toBe(expenseData.title);
      expect(response.body.data.data.amount).toBe(expenseData.amount);
      expect(response.body.data.data.accountId).toBe(expenseData.accountId);
    });

    it('should create expense with installments', async () => {
      const expenseData = {
        title: 'TV Purchase',
        amount: 1200.0,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
        category: 'Electronics',
        isInstallment: true,
        installments: 12,
      };

      const response = await client
        .request()
        .post('/expenses')
        .set('Authorization', `Bearer ${user.token}`)
        .send(expenseData)
        .expect(201);

      expect(response.body.data.data).toBeInstanceOf(Array);
      expect(response.body.data.data).toHaveLength(expenseData.installments);
      expect(response.body.data.data[0]).toHaveProperty('id');
      expect(response.body.data.data[0].title).toContain('TV Purchase (1/12)');
      expect(response.body.data.data[0].amount).toBe(100);
      expect(response.body.data.data[11].title).toContain('TV Purchase (12/12)');
    });

    it('should fail without authentication', async () => {
      const expenseData = {
        title: 'Grocery shopping',
        amount: 50.75,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
        category: 'Food',
      };

      await client.request().post('/expenses').send(expenseData).expect(401);
    });

    it('should fail with missing required fields', async () => {
      const expenseData = {
        title: 'Grocery shopping',
        // missing amount, date, and accountId
      };

      await client
        .request()
        .post('/expenses')
        .set('Authorization', `Bearer ${user.token}`)
        .send(expenseData)
        .expect(400);
    });

    it('should fail with invalid account ID', async () => {
      const expenseData = {
        title: 'Grocery shopping',
        amount: 50.75,
        date: '2025-01-15T10:00:00.000Z',
        accountId: 'invalid-account-id',
        category: 'Food',
      };

      await client
        .request()
        .post('/expenses')
        .set('Authorization', `Bearer ${user.token}`)
        .send(expenseData)
        .expect(400);
    });
  });

  describe('GET /expenses', () => {
    it('should return user expenses', async () => {
      // Create a few expenses
      await client.createExpense(user, {
        title: 'Expense 1',
        amount: 100,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
      });
      await client.createExpense(user, {
        title: 'Expense 2',
        amount: 200,
        date: '2025-01-16T10:00:00.000Z',
        accountId: account.id,
      });

      const response = await client
        .request()
        .get('/expenses')
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(Array.isArray(response.body.data.data)).toBe(true);
      expect(response.body.data.data).toHaveLength(2);
      expect(response.body.data.data[0]).toHaveProperty('id');
      expect(response.body.data.data[0]).toHaveProperty('title');
    });

    it('should return empty array when user has no expenses', async () => {
      const response = await client
        .request()
        .get('/expenses')
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(Array.isArray(response.body.data.data)).toBe(true);
      expect(response.body.data.data).toHaveLength(0);
    });

    it('should fail without authentication', async () => {
      await client.request().get('/expenses').expect(401);
    });
  });

  describe('GET /expenses/:id', () => {
    it('should return specific expense details', async () => {
      const expense = await client.createExpense(user, {
        title: 'Test Expense',
        amount: 150,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
      });

      const response = await client
        .request()
        .get(`/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(response.body.data.data.id).toBe(expense.id);
      expect(response.body.data.data.title).toBe(expense.title);
    });

    it('should fail when accessing non-existent expense', async () => {
      const fakeId = 'non-existent-id';

      await client
        .request()
        .get(`/expenses/${fakeId}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(404);
    });

    it('should fail without authentication', async () => {
      const expense = await client.createExpense(user, {
        title: 'Test Expense',
        amount: 150,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
      });

      await client.request().get(`/expenses/${expense.id}`).expect(401);
    });
  });

  describe('PUT /expenses/:id', () => {
    it('should update expense successfully', async () => {
      const expense = await client.createExpense(user, {
        title: 'Original Expense',
        amount: 100,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
      });

      const updateData = {
        title: 'Updated Expense',
        amount: 150,
        category: 'Updated Category',
      };

      const response = await client
        .request()
        .put(`/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data.data.title).toBe(updateData.title);
      expect(response.body.data.data.amount).toBe(updateData.amount);
    });

    it('should fail when updating non-existent expense', async () => {
      const fakeId = 'non-existent-id';
      const updateData = { title: 'Updated' };

      await client
        .request()
        .put(`/expenses/${fakeId}`)
        .set('Authorization', `Bearer ${user.token}`)
        .send(updateData)
        .expect(400);
    });

    it('should fail without authentication', async () => {
      const expense = await client.createExpense(user, {
        title: 'Test Expense',
        amount: 150,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
      });
      const updateData = { title: 'Updated' };

      await client.request().put(`/expenses/${expense.id}`).send(updateData).expect(401);
    });
  });

  describe('DELETE /expenses/:id', () => {
    it('should delete expense successfully', async () => {
      const expense = await client.createExpense(user, {
        title: 'Test Expense',
        amount: 150,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
      });

      await client
        .request()
        .delete(`/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(204);

      // Verify expense is deleted
      await client
        .request()
        .get(`/expenses/${expense.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(404);
    });

    it('should fail when deleting non-existent expense', async () => {
      const fakeId = 'non-existent-id';

      await client
        .request()
        .delete(`/expenses/${fakeId}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(404);
    });

    it('should fail without authentication', async () => {
      const expense = await client.createExpense(user, {
        title: 'Test Expense',
        amount: 150,
        date: '2025-01-15T10:00:00.000Z',
        accountId: account.id,
      });

      await client.request().delete(`/expenses/${expense.id}`).expect(401);
    });
  });

  describe('Expense filtering and pagination', () => {
    beforeEach(async () => {
      // Create multiple expenses for testing
      for (let i = 1; i <= 15; i++) {
        await client.createExpense(user, {
          title: `Expense ${i}`,
          amount: i * 10,
          date: `2025-01-${i.toString().padStart(2, '0')}T10:00:00.000Z`,
          accountId: account.id,
        });
      }
    });

    it('should filter expenses by date range', async () => {
      const response = await client
        .request()
        .get('/expenses?startDate=2025-01-05&endDate=2025-01-10')
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(Array.isArray(response.body.data.data)).toBe(true);
      expect(response.body.data.data.length).toBeLessThanOrEqual(6); // Days 5-10
    });

    it('should paginate expenses', async () => {
      const response = await client
        .request()
        .get('/expenses?page=1&limit=5')
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(Array.isArray(response.body.data.data)).toBe(true);
      expect(response.body.data.data.length).toBeLessThanOrEqual(5);
    });
  });
});
