import { TestClient } from '../helpers/testClient';

describe('Authentication Integration Tests', () => {
  let client: TestClient;

  beforeEach(() => {
    client = new TestClient();
  });

  describe('POST /auth/register', () => {
    it('should register a new user successfully', async () => {
      const userData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'password123',
      };

      const response = await client
        .request()
        .post('/auth/register')
        .send(userData)
        .expect(201);

      expect(response.body.data.user).toHaveProperty('id');
      expect(response.body.data.user.name).toBe(userData.name);
      expect(response.body.data.user.email).toBe(userData.email);
      expect(response.body.data.user).not.toHaveProperty('password');
    });

    it('should fail with invalid email format', async () => {
      const userData = {
        name: '<PERSON>',
        email: 'invalid-email',
        password: 'password123',
      };

      await client.request().post('/auth/register').send(userData).expect(400);
    });

    it('should fail with missing required fields', async () => {
      const userData = {
        name: '<PERSON> <PERSON>e',
        // missing email and password
      };

      await client.request().post('/auth/register').send(userData).expect(400);
    });

    it('should fail when registering with existing email', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
      };

      // Register first user
      await client.request().post('/auth/register').send(userData).expect(201);

      // Try to register again with same email
      await client.request().post('/auth/register').send(userData).expect(400);
    });
  });

  describe('POST /auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
      };

      // Register user first
      await client.request().post('/auth/register').send(userData).expect(201);

      // Login
      const response = await client
        .request()
        .post('/auth/login')
        .send({
          email: userData.email,
          password: userData.password,
        })
        .expect(200);

      expect(response.body.data).toHaveProperty('token');
      expect(typeof response.body.data.token).toBe('string');
    });

    it('should fail with invalid credentials', async () => {
      const userData = {
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'password123',
      };

      // Register user first
      await client.request().post('/auth/register').send(userData).expect(201);

      // Try login with wrong password
      await client
        .request()
        .post('/auth/login')
        .send({
          email: userData.email,
          password: 'wrongpassword',
        })
        .expect(401);
    });

    it('should fail with non-existent user', async () => {
      await client
        .request()
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'password123',
        })
        .expect(401);
    });
  });

  describe('GET /auth/me', () => {
    it('should return user profile when authenticated', async () => {
      const user = await client.registerUser();

      const response = await client
        .request()
        .get('/auth/me')
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(response.body.data.user.id).toBe(user.id);
      expect(response.body.data.user.email).toBe(user.email);
      expect(response.body.data.user.name).toBe(user.name);
    });

    it('should fail without authentication token', async () => {
      await client.request().get('/auth/me').expect(401);
    });

    it('should fail with invalid token', async () => {
      await client
        .request()
        .get('/auth/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });
  });

  describe('PUT /auth/profile', () => {
    it('should update user profile successfully', async () => {
      const user = await client.registerUser();

      const updateData = {
        name: 'John Updated',
      };

      const response = await client
        .request()
        .put('/auth/profile')
        .set('Authorization', `Bearer ${user.token}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data.updated.name).toBe(updateData.name);
      expect(response.body.data.updated.email).toBe(user.email);
    });

    it('should fail without authentication', async () => {
      const updateData = {
        name: 'John Updated',
      };

      await client.request().put('/auth/profile').send(updateData).expect(401);
    });
  });

  describe('Internationalization', () => {
    it('should return error messages in English by default', async () => {
      const response = await client
        .request()
        .post('/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'wrong',
        })
        .expect(401);

      expect(response.body.error).toBeDefined();
      expect(typeof response.body.error).toBe('string');
    });

    it('should return error messages in Portuguese when specified', async () => {
      const response = await client
        .request()
        .post('/auth/login')
        .set('Accept-Language', 'pt')
        .send({
          email: '<EMAIL>',
          password: 'wrong',
        })
        .expect(401);

      expect(response.body.error).toBeDefined();
      expect(typeof response.body.error).toBe('string');
    });
  });
});
