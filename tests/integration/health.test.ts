import { TestClient } from '../helpers/testClient';

describe('Health Check Integration Tests', () => {
  let client: TestClient;

  beforeEach(() => {
    client = new TestClient();
  });

  describe('GET /health', () => {
    it('should return 200 OK for health check', async () => {
      const response = await client.request().get('/health').expect(200);

      expect(response.body).toBeDefined();
    });

    it('should work without authentication', async () => {
      await client.request().get('/health').expect(200);
    });
  });

  describe('API 404 handling', () => {
    it('should return 404 for non-existent endpoints', async () => {
      await client.request().get('/non-existent-endpoint').expect(404);
    });

    it('should return 404 for invalid API paths', async () => {
      await client.request().get('/api/invalid').expect(404);
    });
  });
});
