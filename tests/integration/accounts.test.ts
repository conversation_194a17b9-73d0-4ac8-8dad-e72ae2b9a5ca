import { TestClient, TestUser } from '../helpers/testClient';

describe('Accounts Integration Tests', () => {
  let client: TestClient;
  let user: TestUser;

  beforeEach(async () => {
    client = new TestClient();
    user = await client.registerUser();
  });

  describe('POST /accounts', () => {
    it('should create a new account successfully', async () => {
      const accountData = {
        name: 'Checking Account',
        type: 'CHECKING',
        bank: 'Test Bank',
      };

      const response = await client
        .request()
        .post('/accounts')
        .set('Authorization', `Bearer ${user.token}`)
        .send(accountData)
        .expect(201);

      expect(response.body.data.account).toHaveProperty('id');
      expect(response.body.data.account.name).toBe(accountData.name);
      expect(response.body.data.account.type).toBe(accountData.type);
      expect(response.body.data.account.bank).toBe(accountData.bank);
    });

    it('should fail without authentication', async () => {
      const accountData = {
        name: 'Checking Account',
        type: 'CHECKING',
        bank: 'Test Bank',
      };

      await client.request().post('/accounts').send(accountData).expect(401);
    });

    it('should fail with missing required fields', async () => {
      const accountData = {
        name: 'Checking Account',
        // missing type and bank
      };

      await client
        .request()
        .post('/accounts')
        .set('Authorization', `Bearer ${user.token}`)
        .send(accountData)
        .expect(400);
    });
  });

  describe('GET /accounts', () => {
    it('should return user accounts', async () => {
      // Create a few accounts
      await client.createAccount(user, { name: 'Account 1', type: 'CHECKING', bank: 'Bank 1' });
      await client.createAccount(user, { name: 'Account 2', type: 'SAVINGS', bank: 'Bank 2' });

      const response = await client
        .request()
        .get('/accounts')
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(Array.isArray(response.body.data.accounts)).toBe(true);
      expect(response.body.data.accounts).toHaveLength(2);
      expect(response.body.data.accounts[0]).toHaveProperty('id');
      expect(response.body.data.accounts[0]).toHaveProperty('name');
    });

    it('should return empty array when user has no accounts', async () => {
      const response = await client
        .request()
        .get('/accounts')
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(Array.isArray(response.body.data.accounts)).toBe(true);
      expect(response.body.data.accounts).toHaveLength(0);
    });

    it('should fail without authentication', async () => {
      await client.request().get('/accounts').expect(401);
    });
  });

  describe('GET /accounts/:id', () => {
    it('should return specific account details', async () => {
      const account = await client.createAccount(user);

      const response = await client
        .request()
        .get(`/accounts/${account.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(200);

      expect(response.body.data.id).toBe(account.id);
      expect(response.body.data.name).toBe(account.name);
    });

    it('should fail when accessing non-existent account', async () => {
      const fakeId = 'non-existent-id';

      await client
        .request()
        .get(`/accounts/${fakeId}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(404);
    });

    it('should fail without authentication', async () => {
      const account = await client.createAccount(user);

      await client.request().get(`/accounts/${account.id}`).expect(401);
    });
  });

  describe('PUT /accounts/:id', () => {
    it('should update account successfully', async () => {
      const account = await client.createAccount(user);

      const updateData = {
        name: 'Updated Account Name',
        type: 'SAVINGS',
      };

      const response = await client
        .request()
        .put(`/accounts/${account.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .send(updateData)
        .expect(200);

      expect(response.body.data.account.name).toBe(updateData.name);
      expect(response.body.data.account.type).toBe(updateData.type);
      expect(response.body.data.account.bank).toBe(account.bank); // Should remain unchanged
    });

    it('should fail when updating non-existent account', async () => {
      const fakeId = 'non-existent-id';
      const updateData = { name: 'Updated Name' };

      await client
        .request()
        .put(`/accounts/${fakeId}`)
        .set('Authorization', `Bearer ${user.token}`)
        .send(updateData)
        .expect(404);
    });

    it('should fail without authentication', async () => {
      const account = await client.createAccount(user);
      const updateData = { name: 'Updated Name' };

      await client.request().put(`/accounts/${account.id}`).send(updateData).expect(401);
    });
  });

  describe('DELETE /accounts/:id', () => {
    it('should delete account successfully', async () => {
      const account = await client.createAccount(user);

      await client
        .request()
        .delete(`/accounts/${account.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(204);

      // Verify account is deleted
      await client
        .request()
        .get(`/accounts/${account.id}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(404);
    });

    it('should fail when deleting non-existent account', async () => {
      const fakeId = 'non-existent-id';

      await client
        .request()
        .delete(`/accounts/${fakeId}`)
        .set('Authorization', `Bearer ${user.token}`)
        .expect(404);
    });

    it('should fail without authentication', async () => {
      const account = await client.createAccount(user);

      await client.request().delete(`/accounts/${account.id}`).expect(401);
    });
  });
});
