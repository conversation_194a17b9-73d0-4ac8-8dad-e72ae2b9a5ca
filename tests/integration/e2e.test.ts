import { TestClient } from '../helpers/testClient';

describe('End-to-End Integration Tests', () => {
  let client: TestClient;

  beforeEach(() => {
    client = new TestClient();
  });

  describe('Complete User Journey', () => {
    it('should complete a full user workflow', async () => {
      // 1. Register a new user
      const userData = {
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'securepassword123',
      };

      const registerResponse = await client.request().post('/auth/register').send(userData).expect(201);

      expect(registerResponse.body.data.user).toHaveProperty('id');
      expect(registerResponse.body.data.user.email).toBe(userData.email);

      // 2. Login with the user
      const loginResponse = await client
        .request()
        .post('/auth/login')
        .send({
          email: userData.email,
          password: userData.password,
        })
        .expect(200);

      const token = loginResponse.body.data.token;
      expect(token).toBeDefined();

      // 3. Get user profile
      const profileResponse = await client
        .request()
        .get('/auth/me')
        .set('Authorization', `Bear<PERSON> ${token}`)
        .expect(200);

      expect(profileResponse.body.data.user.email).toBe(userData.email);

      // 4. Create multiple accounts
      const checkingAccount = await client
        .request()
        .post('/accounts')
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'Main Checking',
          type: 'CHECKING',
          bank: 'Chase Bank',
        })
        .expect(201);

      const savingsAccount = await client
        .request()
        .post('/accounts')
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'Emergency Savings',
          type: 'SAVINGS',
          bank: 'Wells Fargo',
        })
        .expect(201);

      // 5. List all accounts
      const accountsResponse = await client
        .request()
        .get('/accounts')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(accountsResponse.body.data.accounts).toHaveLength(2);

      // 6. Create expenses for different accounts
      const groceryExpense = await client
        .request()
        .post('/expenses')
        .set('Authorization', `Bearer ${token}`)
        .send({
          title: 'Weekly groceries',
          amount: 150.75,
          date: '2025-01-15T00:00:00.000Z',
          accountId: checkingAccount.body.data.account.id,
          category: 'Food',
          familyId: null,
        })
        .expect(201);

      const installmentExpense = await client
        .request()
        .post('/expenses')
        .set('Authorization', `Bearer ${token}`)
        .send({
          title: 'New laptop',
          amount: 2400.0,
          date: '2025-01-16T00:00:00.000Z',
          accountId: checkingAccount.body.data.account.id,
          category: 'Electronics',
          isInstallment: true,
          installments: 12,
          familyId: null,
        })
        .expect(201);

      // 7. List all expenses
      const expensesResponse = await client
        .request()
        .get('/expenses')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(expensesResponse.body.data.data.length).toBeGreaterThanOrEqual(2);

      // 8. Update an expense
      const updatedExpense = await client
        .request()
        .put(`/expenses/${groceryExpense.body.data.data.id}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          title: 'Weekly groceries - Updated',
          amount: 175.0,
          category: 'Food & Dining',
        })
        .expect(200);

      expect(updatedExpense.body.data.data.title).toBe('Weekly groceries - Updated');
      expect(updatedExpense.body.data.data.amount).toBe(175.0);

      // 9. Get specific expense details
      const expenseDetails = await client
        .request()
        .get(`/expenses/${installmentExpense.body.data.data[0].id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(expenseDetails.body.data.data.installmentInfo.total).toBe(12);

      // 10. Update user profile
      const updatedProfile = await client
        .request()
        .put('/auth/profile')
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'Alice Johnson Smith',
        })
        .expect(200);

      expect(updatedProfile.body.data.updated.name).toBe('Alice Johnson Smith');

      // 11. Update an account
      const updatedAccount = await client
        .request()
        .put(`/accounts/${savingsAccount.body.data.account.id}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          name: 'High-Yield Emergency Savings',
          type: 'SAVINGS',
        })
        .expect(200);

      expect(updatedAccount.body.data.account.name).toBe('High-Yield Emergency Savings');

      // 12. Delete an expense
      await client
        .request()
        .delete(`/expenses/${groceryExpense.body.data.data.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(204);

      // 13. Verify expense was deleted
      await client
        .request()
        .get(`/expenses/${groceryExpense.body.data.data.id}`)
        .set('Authorization', `Bearer ${token}`)
        .expect(404);

      // 14. Final verification - list remaining expenses
      const finalExpensesResponse = await client
        .request()
        .get('/expenses')
        .set('Authorization', `Bearer ${token}`)
        .expect(200);

      expect(finalExpensesResponse.body.data.data.length).toBeGreaterThanOrEqual(1);
    });
  });

  describe('Multi-user scenarios', () => {
    it('should isolate data between different users', async () => {
      // Create first user and their data
      const user1 = await client.registerUser({
        name: 'User One',
        email: '<EMAIL>',
        password: 'password123',
      });

      const user1Account = await client.createAccount(user1, {
        name: 'User 1 Account',
        type: 'CHECKING',
        bank: 'Bank 1',
      });

      await client.createExpense(user1, {
        title: 'User 1 Expense',
        amount: 100,
        date: '2025-01-15T00:00:00.000Z',
        accountId: user1Account.id,
      });

      // Create second user and their data
      const user2 = await client.registerUser({
        name: 'User Two',
        email: '<EMAIL>',
        password: 'password123',
      });

      const user2Account = await client.createAccount(user2, {
        name: 'User 2 Account',
        type: 'SAVINGS',
        bank: 'Bank 2',
      });

      await client.createExpense(user2, {
        title: 'User 2 Expense',
        amount: 200,
        date: '2025-01-16T00:00:00.000Z',
        accountId: user2Account.id,
      });

      // Verify User 1 can only see their data
      const user1Accounts = await client
        .request()
        .get('/accounts')
        .set('Authorization', `Bearer ${user1.token}`)
        .expect(200);

      expect(user1Accounts.body.data.accounts).toHaveLength(1);
      expect(user1Accounts.body.data.accounts[0].name).toBe('User 1 Account');

      const user1Expenses = await client
        .request()
        .get('/expenses')
        .set('Authorization', `Bearer ${user1.token}`)
        .expect(200);

      expect(user1Expenses.body.data.data).toHaveLength(1);
      expect(user1Expenses.body.data.data[0].title).toBe('User 1 Expense');

      // Verify User 2 can only see their data
      const user2Accounts = await client
        .request()
        .get('/accounts')
        .set('Authorization', `Bearer ${user2.token}`)
        .expect(200);

      expect(user2Accounts.body.data.accounts).toHaveLength(1);
      expect(user2Accounts.body.data.accounts[0].name).toBe('User 2 Account');

      const user2Expenses = await client
        .request()
        .get('/expenses')
        .set('Authorization', `Bearer ${user2.token}`)
        .expect(200);

      expect(user2Expenses.body.data.data).toHaveLength(1);
      expect(user2Expenses.body.data.data[0].title).toBe('User 2 Expense');

      // Verify User 1 cannot access User 2's account
      await client
        .request()
        .get(`/accounts/${user2Account.id}`)
        .set('Authorization', `Bearer ${user1.token}`)
        .expect(404);
    });
  });
});
