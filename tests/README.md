# 🧪 Testing Documentation

This directory contains the comprehensive test suite for the Granik Backend API. The test suite ensures 100% API coverage and validates all functionality with integration tests.

## 📊 Test Overview

**Total Tests: 54** ✅ **All Passing**

### Test Suites

| Suite              | Tests | Coverage | Description                                |
| ------------------ | ----- | -------- | ------------------------------------------ |
| **Authentication** | 14    | 100%     | User registration, login, profile, i18n    |
| **Accounts**       | 15    | 100%     | CRUD operations, validation, authorization |
| **Expenses**       | 19    | 100%     | CRUD, installments, pagination, filtering  |
| **Health**         | 4     | 100%     | API availability, error handling           |
| **End-to-End**     | 2     | 100%     | Complete workflows, multi-user isolation   |

The integration tests are designed to test the API endpoints by making actual HTTP requests to a test instance of the application. These tests verify:

- Authentication and authorization flows
- CRUD operations for all resources
- Data validation and error handling
- Multi-user scenarios and data isolation
- End-to-end user workflows

## 🏗️ Test Architecture

### Structure

```
tests/
├── helpers/
│   ├── database.ts        # Database utilities for test isolation
│   └── testClient.ts      # Test client with helper methods
├── integration/
│   ├── auth.test.ts       # Authentication endpoint tests
│   ├── accounts.test.ts   # Account management tests
│   ├── expenses.test.ts   # Expense tracking tests
│   ├── health.test.ts     # Health check tests
│   └── e2e.test.ts        # End-to-end workflow tests
├── setup.ts               # Global test configuration
└── README.md              # This documentation
```

### Test Environment

- **Database**: SQLite in-memory for test isolation
- **Framework**: Jest with Supertest for HTTP testing
- **Execution**: Serial execution to prevent database conflicts
- **Timeout**: 30 seconds per test for complex operations
- **Isolation**: Each test runs with a clean database state

## 🔧 Configuration

### Jest Configuration (`jest.config.js`)

```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts'],
  testMatch: ['<rootDir>/tests/**/*.test.ts'],
  collectCoverageFrom: ['src/**/*.ts'],
  coverageDirectory: 'coverage',
  maxWorkers: 1, // Serial execution for database safety
  testTimeout: 30000,
};
```

### Environment Variables

Tests use `.env.test` with SQLite for isolation:

```env
DATABASE_URL="file:./test.db"
JWT_SECRET="test_jwt_secret"
```

## 📝 Test Patterns

### TestClient Helper

The `TestClient` class provides convenient methods for testing:

```typescript
const client = new TestClient();

// User management
const user = await client.registerUser({
  name: 'Test User',
  email: '<EMAIL>',
  password: 'password123',
});

// Account management
const account = await client.createAccount(user, {
  name: 'Test Account',
  type: 'CHECKING',
  bank: 'Test Bank',
});

// Expense management
const expense = await client.createExpense(user, {
  title: 'Test Expense',
  amount: 100,
  accountId: account.id,
});
```

### Database Isolation

Each test runs with a fresh database:

```typescript
beforeEach(async () => {
  await clearDatabase(); // Clean slate for each test
});
```

### Response Validation

Tests validate complete response structures:

```typescript
expect(response.body).toEqual({
  data: {
    user: {
      id: expect.any(String),
      name: 'Test User',
      email: '<EMAIL>',
    },
  },
});
```

## 🎯 Test Scenarios

### Authentication Tests (14 tests)

**Registration**

- ✅ Successful user registration
- ✅ Email format validation
- ✅ Required field validation
- ✅ Duplicate email prevention

**Login**

- ✅ Successful authentication
- ✅ Invalid credentials handling
- ✅ Non-existent user handling

**Profile Management**

- ✅ Authenticated profile access
- ✅ Profile updates
- ✅ Authentication token validation

**Internationalization**

- ✅ English error messages (default)
- ✅ Portuguese error messages

### Account Tests (15 tests)

**CRUD Operations**

- ✅ Account creation with all types
- ✅ Account listing (user-specific)
- ✅ Individual account retrieval
- ✅ Account updates
- ✅ Account deletion

**Authorization**

- ✅ Authentication required for all operations
- ✅ User-specific data isolation
- ✅ Cross-user access prevention

**Validation**

- ✅ Required field validation
- ✅ Account type validation
- ✅ Non-existent account handling

### Expense Tests (19 tests)

**Basic CRUD**

- ✅ Single expense creation
- ✅ Installment expense creation
- ✅ Expense listing with pagination
- ✅ Individual expense retrieval
- ✅ Expense updates
- ✅ Expense deletion

**Advanced Features**

- ✅ Date range filtering
- ✅ Pagination (page/limit)
- ✅ Category filtering
- ✅ Installment handling

**Authorization & Validation**

- ✅ Authentication requirements
- ✅ User data isolation
- ✅ Input validation
- ✅ Account ownership validation

### Health Tests (4 tests)

**API Health**

- ✅ Health endpoint availability
- ✅ Unauthenticated access allowed
- ✅ 404 handling for invalid routes
- ✅ API path validation

### End-to-End Tests (2 tests)

**Complete User Journey**

- ✅ Full user workflow (registration → accounts → expenses → updates → deletion)
- ✅ Complex operations (installments, multiple accounts, profile updates)

**Multi-User Isolation**

- ✅ Data separation between users
- ✅ Cross-user access prevention
- ✅ Independent user workflows

## 🚀 Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run specific test file
npm test -- tests/integration/auth.test.ts

# Run tests in watch mode
npm run test:watch

# Run with verbose output
npm test -- --verbose

# Run specific test pattern
npm test -- --testNamePattern="should register"
```

### Debugging Tests

```bash
# Run single test with debugging
npm test -- --testNamePattern="specific test name" --runInBand

# Check test coverage
npm run test:coverage
```

### Test Output Example

```
Test Suites: 5 passed, 5 total
Tests:       54 passed, 54 total
Snapshots:   0 total
Time:        11.697 s
```

## 📋 Test Checklist

When adding new features, ensure:

- [ ] **Unit/Integration Tests** - Test all new endpoints
- [ ] **Authentication** - Verify auth requirements
- [ ] **Authorization** - Test user data isolation
- [ ] **Validation** - Test input validation and error cases
- [ ] **Response Format** - Ensure consistent API responses
- [ ] **Database Isolation** - Tests don't affect each other
- [ ] **Error Handling** - Test all error scenarios
- [ ] **Documentation** - Update API docs if needed

## 🐛 Common Issues & Solutions

### Test Timeouts

If tests timeout, check for:

- Unclosed database connections
- Infinite loops in async operations
- Missing await statements

### Database Conflicts

If tests interfere with each other:

- Ensure `maxWorkers: 1` in Jest config
- Verify `clearDatabase()` in setup
- Check for shared state between tests

### Authentication Errors

For auth-related test failures:

- Verify JWT_SECRET in `.env.test`
- Check token generation in TestClient
- Ensure proper Authorization headers

## 🎉 Test Quality Metrics

- **Coverage**: 100% API endpoint coverage
- **Reliability**: All tests consistently pass
- **Performance**: Complete suite runs in ~12 seconds
- **Maintainability**: Clear test structure and helpers
- **Documentation**: Comprehensive test descriptions

The test suite serves as both quality assurance and living documentation of the API behavior.
