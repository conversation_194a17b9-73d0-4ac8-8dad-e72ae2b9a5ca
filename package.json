{"name": "granik-backend", "version": "1.0.0", "description": "Backend da aplicação de gestão financeira pessoal/familiar", "author": "<PERSON>", "license": "MIT", "main": "src/server.ts", "scripts": {"build": "tsc --noEmit", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "docker": "docker compose up -d", "lint": "eslint \"src/**/*.ts\" --quiet --fix", "prettier": "prettier --write \"src/**/*.{js,ts,tsx}\"", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "start": "npm run docker && npm run ts-node-dev", "test": "jest", "test:integration": "jest tests/integration", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "ts-node-dev": "ts-node-dev --respawn --transpile-only src/server.ts", "watch": "nodemon src/server.ts"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.9", "@types/node": "^22.14.0", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.29.1", "@typescript-eslint/parser": "^8.29.1", "dotenv": "^17.2.0", "esbuild": "^0.25.2", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.2.6", "jest": "^30.0.4", "jiti": "^2.4.2", "prisma": "^6.7.0", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "dependencies": {"@prisma/client": "^6.7.0", "bcrypt": "^5.1.1", "cors": "^2.8.5", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "zod": "^3.24.2"}}