{
  "compilerOptions": {
    "target": "es2020", // could also use es2021?
    "module": "CommonJS", // commonjs looks better for node, as opposed to es2020
    "esModuleInterop": true,
    "removeComments": true,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitOverride": true,
    "noUnusedParameters": true,
    "noUnusedLocals": true,
    "noImplicitReturns": true,
    "strict": true,
    "alwaysStrict": true,
    "noImplicitThis": true,
    "strictBindCallApply": true,
    "strictFunctionTypes": true,
    "strictNullChecks": true,
    "strictPropertyInitialization": true,
    "useUnknownInCatchVariables": true,
    "noImplicitAny": true,
    "baseUrl": ".",
    "sourceMap": false,
    "moduleResolution": "node", // "node" is default for commonjs
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "typeRoots": ["./node_modules/@types", "./@types"],
    "resolveJsonModule": true
  },
  "include": ["src", "@types"],
  "exclude": ["node_modules", "dist"]
}
