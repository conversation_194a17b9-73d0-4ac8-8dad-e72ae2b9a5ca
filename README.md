# 💸 Granik - Backend

**Granik** is a personal and family financial management platform built for simplicity and day-to-day organization.

[![Tests](https://img.shields.io/badge/Tests-54%2F54%20Passing-brightgreen)](#testing)
[![API Coverage](https://img.shields.io/badge/API%20Coverage-Complete-blue)](#api-endpoints)
[![TypeScript](https://img.shields.io/badge/TypeScript-100%25-blue)](#)

---

## 🚀 Technologies

- **Node.js** + **Express** - Web framework
- **TypeScript** - Type safety and developer experience
- **Prisma ORM** - Database toolkit with type safety
- **PostgreSQL** - Primary database (SQLite for testing)
- **JWT** - Authentication and authorization
- **Bcrypt** - Password hashing
- **Jest** + **Supertest** - Comprehensive testing suite
- **Zod** - Schema validation
- **Swagger/OpenAPI** - API documentation

---

## 📁 Project Structure

```
granik-backend/
├── @types/                # Custom type declarations
├── prisma/                # Prisma database schema and migrations
├── src/
│   ├── config/            # Configuration files (Express, routes, Swagger)
│   ├── controllers/       # Route controllers with standardized responses
│   ├── docs/              # Swagger/OpenAPI documentation schemas
│   ├── i18n/              # Internationalization (en_us, pt_br)
│   ├── middlewares/       # Express middlewares (auth, validation, locale)
│   ├── routers/           # Express route definitions
│   ├── services/          # Business logic layer
│   ├── utils/             # Helper utilities (hash, response formatting)
│   ├── validations/       # Zod schemas for request validation
│   └── server.ts          # Server startup
├── tests/                 # Comprehensive test suite
│   ├── helpers/           # Test utilities and client
│   ├── integration/       # Integration tests (54 tests)
│   └── setup.ts           # Test environment configuration
├── .env.example           # Example of required environment variables
├── .env.test              # Test environment configuration
├── docker-compose.yml     # PostgreSQL and other services
├── jest.config.js         # Jest testing configuration
├── eslint.config.ts       # ESLint configuration
├── nodemon.json           # Development server configuration
└── package.json

```

---

## 🔧 How to Run Locally

### 1. Clone the repository

```bash
git clone https://github.com/alexisdiel/granik-backend.git
cd granik-backend
```

### 2. Install dependencies

```bash
npm install
```

### 3. Environment Configuration

Create a `.env` file for development:

```env
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/finance_db"
JWT_SECRET="your_secure_jwt_secret_here"
PORT=3333
```

Create a `.env.test` file for testing:

```env
DATABASE_URL="file:./test.db"
JWT_SECRET="test_jwt_secret"
```

> ⚠️ You can change the database credentials in the `docker-compose.yml` file.

### 4. Start the containers

```bash
docker-compose up -d
```

### 5. Generate the Prisma client and run migrations

```bash
npx prisma generate
npx prisma migrate dev --name init
```

### 6. Start the development server

```bash
npm run dev
```

> The API will be available at `http://localhost:3333`  
> Swagger documentation: `http://localhost:3333/docs`

---

## 🧪 Testing

The project includes a comprehensive test suite with 54 integration tests covering all API endpoints and workflows.

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run specific test file
npm test -- tests/integration/auth.test.ts

# Run with coverage
npm run test:coverage
```

### Test Suites

- **Authentication Tests** (14 tests) - Registration, login, profile management, i18n
- **Account Management Tests** (15 tests) - CRUD operations, validation, authorization
- **Expense Management Tests** (19 tests) - CRUD, installments, pagination, filtering
- **Health Check Tests** (4 tests) - API availability, error handling
- **End-to-End Tests** (2 tests) - Complete user workflows, multi-user data isolation

### Test Features

- **Complete API Coverage** - All endpoints tested with various scenarios
- **Data Isolation** - Each test runs with clean database state
- **Multi-user Testing** - Verifies proper data separation between users
- **Error Handling** - Tests authentication, validation, and authorization failures
- **Response Validation** - Ensures consistent API response structures

---

## ✅ Implemented Features

### 🔐 Authentication & Authorization

- JWT-based authentication with secure password hashing
- User registration and login with validation
- Protected routes with middleware authentication
- User profile management and updates
- Internationalization support (English/Portuguese)

### 💳 Account Management

- Multiple account types (checking, savings, debit, credit)
- Full CRUD operations for financial accounts
- User-specific account isolation
- Account validation and error handling

### 💰 Expense Tracking

- Comprehensive expense logging with categories
- Support for one-time and installment expenses
- Advanced filtering (date ranges, categories, amounts)
- Pagination for large expense lists
- Full CRUD operations with proper authorization

### 👨‍👩‍👧‍👦 Multi-user & Family Support

- Complete user data isolation
- Family-based expense sharing (foundation)
- Proper authorization across all endpoints

### 🛠️ Technical Features

- **Standardized API Responses** - Consistent response format across all endpoints
- **Comprehensive Validation** - Zod schemas for all request/response validation
- **Error Handling** - Proper HTTP status codes and error messages
- **Database Management** - Prisma ORM with migrations and type safety
- **Testing Suite** - 54 integration tests with 100% pass rate
- **API Documentation** - Interactive Swagger/OpenAPI documentation

---

## � Project Status & Development

### ✅ Completed (v1.0)

- [x] Complete authentication system with JWT
- [x] Full account management (CRUD)
- [x] Comprehensive expense tracking with installments
- [x] Multi-user data isolation
- [x] Internationalization (English/Portuguese)
- [x] Complete test suite (54 tests, 100% passing)
- [x] API documentation with Swagger
- [x] Standardized response format
- [x] Input validation and error handling

### 🔄 In Progress

- [ ] Family management system
- [ ] Account balance tracking
- [ ] Monthly/yearly summaries

### 🎯 Next Steps (v2.0)

- [ ] Dashboard with spending analytics
- [ ] Budget management and alerts
- [ ] Income tracking
- [ ] Recurring expenses automation
- [ ] Export/import functionality
- [ ] Mobile app integration
- [ ] Advanced reporting

### 🛠️ Development Workflow

This project follows best practices for Node.js/TypeScript development:

1. **Type Safety** - 100% TypeScript with strict configuration
2. **Testing First** - Comprehensive test coverage for all features
3. **Clean Architecture** - Separation of concerns (controllers, services, utils)
4. **API Standards** - RESTful endpoints with consistent responses
5. **Documentation** - Auto-generated API docs with Swagger
6. **Code Quality** - ESLint, Prettier, and pre-commit hooks

---

## 📌 Next Steps

The following features are planned for future releases:

- Monthly summaries by period
- Enhanced spending dashboard
- Advanced account balance control
- Complete family management system
- Income tracking and categorization
- Smart notifications and alerts

---

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes with proper tests
4. Ensure all tests pass (`npm test`)
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

### Development Guidelines

- Write tests for all new features
- Follow TypeScript best practices
- Maintain API response consistency
- Update documentation for API changes
- Ensure internationalization support

---

---

## 🎯 API Endpoints

Full interactive documentation available at: **[`/docs`](http://localhost:3333/docs)** (Swagger UI)

### 🔐 Authentication

- `POST /auth/register` - User registration with validation
- `POST /auth/login` - User authentication
- `GET /auth/me` - Get current user profile
- `PUT /auth/profile` - Update user profile

### 💳 Accounts

- `GET /accounts` - List user's accounts
- `POST /accounts` - Create new account
- `GET /accounts/:id` - Get specific account details
- `PUT /accounts/:id` - Update account information
- `DELETE /accounts/:id` - Delete account

### 💰 Expenses

- `GET /expenses` - List expenses with filtering and pagination
- `POST /expenses` - Create new expense (supports installments)
- `GET /expenses/:id` - Get specific expense details
- `PUT /expenses/:id` - Update expense information
- `DELETE /expenses/:id` - Delete expense

### 🏥 Health Check

- `GET /health` - API health status

### 📊 Query Parameters

**Expenses Filtering:**

- `?startDate=YYYY-MM-DD` - Filter by start date
- `?endDate=YYYY-MM-DD` - Filter by end date
- `?category=string` - Filter by category
- `?page=number` - Pagination page
- `?limit=number` - Items per page

### 📝 Response Format

All API responses follow a standardized format:

```json
{
  "data": {
    // Response data here
  }
}
```

**Error responses:**

```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

---

---

## 🚀 Quick Start

```bash
# Install dependencies
npm install

# Set up environment
cp .env.example .env
cp .env.example .env.test

# Start database
docker-compose up -d

# Run database migrations
npx prisma generate
npx prisma migrate dev --name init

# Run tests to verify setup
npm test

# Start development server
npm run dev
```

### 📋 Available Scripts

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build for production
npm run start        # Start production server
npm test             # Run test suite
npm run test:watch   # Run tests in watch mode
npm run lint         # Run ESLint
npm run lint:fix     # Fix ESLint issues
npm run db:reset     # Reset database
npm run db:seed      # Seed database (if applicable)
```

---

## 🧑‍💻 Developed by

Alexis Diel  
[LinkedIn](https://linkedin.com/in/alexis-diel) · [GitHub](https://github.com/alexisdiel)
