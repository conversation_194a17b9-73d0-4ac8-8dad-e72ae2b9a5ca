# Granik UI

A modern, responsive frontend for Granik - a personal and family finance tracking application.

## 🚀 Features

- **Authentication**: Secure JWT-based login and registration
- **Dashboard**: Financial overview with account balances and recent transactions
- **Account Management**: Support for checking, savings, credit cards, and investment accounts
- **Expense Tracking**: Categorized expense management with filtering and search
- **Pro Features**: Stripe integration for premium functionality
- **Responsive Design**: Optimized for desktop and mobile devices

## 🛠️ Tech Stack

- **Frontend**: React 18 + TypeScript + Vite
- **Styling**: TailwindCSS with custom component classes
- **Routing**: React Router v6 with protected routes
- **State Management**: Zustand for lightweight global state
- **API Client**: Axios with interceptors for auth and error handling
- **Build Tool**: Vite for fast development and optimized builds

## 📁 Project Structure

```
src/
├── components/     # Reusable UI components
├── config/         # App configuration and constants
├── hooks/          # Custom React hooks
├── pages/          # Page components for routing
├── services/       # API clients and external services
├── store/          # Zustand state management
└── utils/          # Helper functions and utilities
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+ and npm
- Access to the Granik backend API

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/granik-ui.git
cd granik-ui
```

2. Install dependencies:

```bash
npm install
```

3. Create environment file:

```bash
cp .env.example .env
```

4. Update `.env` with your configuration:

```bash
VITE_API_BASE_URL=http://localhost:3333
VITE_STRIPE_PUBLISHABLE_KEY=your_stripe_key_here
```

5. Start the development server:

```bash
npm run dev
```

The application will be available at `http://localhost:5173`

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript type checking

## 🏗️ Backend Integration

This frontend connects to the Granik backend API for:

- User authentication and authorization
- Account and transaction management
- User preferences and settings
- Stripe payment processing

API documentation: [Backend Repository](https://github.com/your-username/granik-backend)

## 🎨 Design System

The application uses a custom design system built on TailwindCSS:

- **Colors**: Primary blue palette with semantic color tokens
- **Typography**: Inter font family with consistent sizing scale
- **Components**: Reusable component classes (`.btn-primary`, `.card`, `.input-field`)
- **Spacing**: Consistent spacing using Tailwind's spacing scale

## 🔐 Authentication

- JWT token-based authentication with automatic refresh
- Tokens stored in localStorage with expiration handling
- Protected routes redirect to login when unauthenticated
- Automatic logout on token expiration

## 🚀 Deployment

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory, ready for deployment to any static hosting service.

### Environment Variables

Required environment variables for production:

- `VITE_API_BASE_URL` - Backend API URL
- `VITE_STRIPE_PUBLISHABLE_KEY` - Stripe publishable key
- `VITE_APP_NAME` - Application name
- `VITE_APP_VERSION` - Application version

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:

- Open an issue on GitHub
- Contact the development team
- Check the documentation wiki
