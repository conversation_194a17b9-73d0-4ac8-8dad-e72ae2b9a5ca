services:
  db:
    image: postgres:15
    container_name: granik_db
    restart: always
    environment:
      POSTGRES_USER: granik_user
      POSTGRES_PASSWORD: granik_pass
      POSTGRES_DB: granik
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - granik-net

  pgadmin:
    image: dpage/pgadmin4
    container_name: pgadmin
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
    ports:
      - "5050:80"
    depends_on:
      - db
    networks:
      - granik-net

volumes:
  pgdata:

networks:
  granik-net:
