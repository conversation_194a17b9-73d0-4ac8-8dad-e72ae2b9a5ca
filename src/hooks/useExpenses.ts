import { useState, useEffect, useCallback } from 'react';
import { api } from '../services/api';
import type { Expense } from '../types';

interface UseExpensesReturn {
  expenses: Expense[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useExpenses = (): UseExpensesReturn => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchExpenses = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🔍 Fetching expenses from API...');
      const token = localStorage.getItem('granik_auth_token');
      console.log('🔑 Auth token:', token ? `Present (${token.substring(0, 20)}...)` : 'Missing');

      if (!token) {
        setError('No authentication token found. Please log in again.');
        return;
      }

      const response = await api.getExpenses();

      console.log('📡 API Response:', response);

      // Handle the response - check if we have data regardless of success field
      try {
        let expensesData: Expense[] = [];

        // Try different response structures
        if (response.data) {
          if (Array.isArray(response.data)) {
            expensesData = response.data;
          } else if (typeof response.data === 'object' && 'data' in response.data) {
            expensesData = (response.data as { data: Expense[] }).data || [];
          }
        }

        const sortedExpenses = expensesData.sort((a: Expense, b: Expense) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        setExpenses(sortedExpenses);
        console.log('✅ Expenses loaded successfully:', sortedExpenses.length, 'items');

        // If we got here, everything worked
        return;
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
      }

      // If we reach here, something went wrong
      const errorMsg = (response as { message?: string }).message || 'Failed to fetch expenses';
      console.error('❌ API Error:', errorMsg);
      setError(errorMsg);
    } catch (err) {
      console.error('❌ Network/Request Error:', err);
      setError('Failed to fetch expenses. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchExpenses();
  }, [fetchExpenses]);

  return {
    expenses,
    isLoading,
    error,
    refetch: fetchExpenses,
  };
};
