import { useState, useEffect, useCallback } from 'react';
import { api } from '../services/api';
import type { Expense } from '../types';

interface ExpenseFilters {
  accountId?: string;
  startDate?: string;
  endDate?: string;
  category?: string;
}

interface UseExpensesReturn {
  expenses: Expense[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  setPage: (page: number) => void;
  filters: ExpenseFilters;
  setFilters: (filters: ExpenseFilters) => void;
  clearFilters: () => void;
}

export const useExpenses = (): UseExpensesReturn => {
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [filters, setFiltersState] = useState<ExpenseFilters>({});
  const itemsPerPage = 10;

  const fetchExpenses = useCallback(async (page?: number) => {
    try {
      setIsLoading(true);
      setError(null);

      const pageToFetch = page || currentPage;

      console.log('🔍 Fetching expenses from API...');
      const token = localStorage.getItem('granik_auth_token');
      console.log('🔑 Auth token:', token ? `Present (${token.substring(0, 20)}...)` : 'Missing');

      if (!token) {
        setError('No authentication token found. Please log in again.');
        return;
      }

      const response = await api.getExpenses({
        page: pageToFetch,
        limit: itemsPerPage,
        ...filters,
      });

      console.log('📡 API Response:', response);

      // Handle the response - check if we have data regardless of success field
      try {
        let expensesData: Expense[] = [];
        let paginationData = {
          total: 0,
          page: pageToFetch,
          totalPages: 1,
        };

        // Try different response structures
        if (response.data) {
          if (Array.isArray(response.data)) {
            expensesData = response.data;
            // If it's just an array, assume no pagination
            paginationData.total = response.data.length;
          } else if (typeof response.data === 'object') {
            const dataObj = response.data as any;
            if ('data' in dataObj) {
              expensesData = dataObj.data || [];
            }
            if ('pagination' in dataObj) {
              paginationData = { ...paginationData, ...dataObj.pagination };
            } else if ('total' in dataObj) {
              paginationData.total = dataObj.total;
              paginationData.totalPages = Math.ceil(dataObj.total / itemsPerPage);
            }
          }
        }

        const sortedExpenses = expensesData.sort((a: Expense, b: Expense) =>
          new Date(b.date).getTime() - new Date(a.date).getTime()
        );
        setExpenses(sortedExpenses);
        setTotalItems(paginationData.total);
        setCurrentPage(pageToFetch);
        console.log('✅ Expenses loaded successfully:', sortedExpenses.length, 'items, page', pageToFetch);

        // If we got here, everything worked
        return;
      } catch (parseError) {
        console.error('Error parsing response:', parseError);
      }

      // If we reach here, something went wrong
      const errorMsg = (response as { message?: string }).message || 'Failed to fetch expenses';
      console.error('❌ API Error:', errorMsg);
      setError(errorMsg);
    } catch (err) {
      console.error('❌ Network/Request Error:', err);
      setError('Failed to fetch expenses. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  useEffect(() => {
    fetchExpenses();
  }, [fetchExpenses]);

  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const setPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages) {
      fetchExpenses(page);
    }
  }, [fetchExpenses, totalPages]);

  const setFilters = useCallback((newFilters: ExpenseFilters) => {
    setFiltersState(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  }, []);

  const clearFilters = useCallback(() => {
    setFiltersState({});
    setCurrentPage(1);
  }, []);

  return {
    expenses,
    isLoading,
    error,
    refetch: fetchExpenses,
    pagination: {
      currentPage,
      totalPages,
      totalItems,
      itemsPerPage,
      hasNextPage: currentPage < totalPages,
      hasPrevPage: currentPage > 1,
    },
    setPage,
    filters,
    setFilters,
    clearFilters,
  };
};
