import { useState, useEffect, useCallback } from 'react';
import { api } from '../services/api';

interface Account {
  id: string;
  name: string;
  bank: string;
  type: string;
  balance?: number;
}

interface UseAccountsReturn {
  accounts: Account[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useAccounts = (): UseAccountsReturn => {
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAccounts = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.getAccounts();
      
      let accountsData: Account[] = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          accountsData = response.data;
        } else if (typeof response.data === 'object' && 'data' in response.data) {
          accountsData = (response.data as { data: Account[] }).data || [];
        }
      }
      
      setAccounts(accountsData);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      setError('Failed to fetch accounts. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  return {
    accounts,
    isLoading,
    error,
    refetch: fetchAccounts,
  };
};
