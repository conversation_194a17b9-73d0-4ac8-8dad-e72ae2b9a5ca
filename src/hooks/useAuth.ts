import { useAuthStore } from '../store/authStore';
import type { LoginRequest, RegisterRequest } from '../services/api';

export const useAuth = () => {
  const {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    login,
    register,
    logout,
    clearError,
    setUser,
  } = useAuthStore();

  return {
    // State
    user,
    token,
    isAuthenticated,
    isLoading,
    error,

    // Computed
    isProUser: user?.isProUser || false,

    // Actions
    login: async (credentials: LoginRequest) => {
      try {
        await login(credentials);
        return { success: true };
      } catch (error) {
        return { success: false, error };
      }
    },

    register: async (userData: RegisterRequest) => {
      try {
        await register(userData);
        return { success: true };
      } catch (error) {
        return { success: false, error };
      }
    },

    logout,
    clearError,
    setUser,

    // Utility methods
    hasPermission: (permission: string) => {
      // Basic permission check - extend as needed
      if (!isAuthenticated) return false;
      if (permission === 'pro' && !user?.isProUser) return false;
      return true;
    },

    requireAuth: () => {
      if (!isAuthenticated) {
        throw new Error('Authentication required');
      }
    },

    requirePro: () => {
      if (!isAuthenticated || !user?.isProUser) {
        throw new Error('Pro subscription required');
      }
    },
  };
};
