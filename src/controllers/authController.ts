import { Request, Response } from 'express';
import { loginUser, registerUser } from '../services/authService';
import { getUserById, updateUserProfile } from '../services/userService';
import { response } from '../utils/response';

export const register = async (req: Request, res: Response) => {
  const { name, email, password } = req.body;

  if (!name || !email || !password) {
    response.error(req, res, 'missingFields');
    return;
  }

  try {
    const user = await registerUser(name, email, password);
    response.created(req, res, { user });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
      if (err.message === 'Email already registered') {
        response.error(req, res, 'badRequest');
        return;
      }
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};

export const login = async (req: Request, res: Response) => {
  const { email, password } = req.body;

  if (!email || !password) {
    response.error(req, res, 'missingCredentials');
    return;
  }

  try {
    const { token } = await loginUser(email, password);
    response.success(req, res, { token });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.unauthorized(req, res, 'unauthorized');
  }
};

export const me = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;

  try {
    const user = await getUserById(userId!);
    response.success(req, res, { user });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'internalServerError', 500);
  }
};

export const updateProfile = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const { name, email, password } = req.body;

  try {
    const updated = await updateUserProfile(userId!, { name, email, password });
    response.success(req, res, { updated });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};
