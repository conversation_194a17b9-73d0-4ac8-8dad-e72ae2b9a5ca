import { Request, Response } from 'express';
import {
  createExpense,
  createInstallments,
  listExpense,
  listExpenses,
  listInstallments,
  updateExpense,
  removeExpense,
} from '../services/expenseService';
import { response } from '../utils/response';

export const getExpenses = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const { accountId, startDate, endDate, category, familyId, page, limit } = req.query;

  try {
    const expenses = await listExpenses({
      userId: userId!,
      familyId: familyId as string,
      accountId: accountId as string,
      category: category as string,
      startDate: startDate as string,
      endDate: endDate as string,
      page: page ? parseInt(page as string, 10) : undefined,
      limit: limit ? parseInt(limit as string, 10) : undefined,
    });

    response.success(req, res, { data: expenses });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'internalServerError', 500);
  }
};

export const getExpense = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const { id } = req.params;

  if (!userId) {
    response.unauthorized(req, res, 'unauthorized');
    return;
  }

  try {
    const expense = await listExpense(userId, id);
    if (!expense) {
      response.notFound(req, res, 'expenseNotFound');
      return;
    }

    response.success(req, res, { data: expense });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'internalServerError', 500);
  }
};

export const postExpense = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const { title, amount, category, date, accountId, notes, isInstallment, installments, familyId } =
    req.body;

  try {
    const parsedDate = new Date(date);

    if (isInstallment && installments > 1) {
      const result = await createInstallments({
        userId: userId!,
        familyId,
        title,
        amount,
        category,
        date: parsedDate,
        accountId,
        notes,
        installments,
      });

      response.created(req, res, { data: result }, 'createdInstallments');
      return;
    }

    const result = await createExpense({
      userId: userId!,
      familyId,
      title,
      amount,
      category,
      date: parsedDate,
      accountId,
      notes,
    });

    response.created(req, res, { data: result }, 'expenseCreated');
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};

export const putExpense = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { id: userId } = req.user!;
  const updates = req.body;

  try {
    const updated = await updateExpense(id, userId!, updates);
    response.success(req, res, { data: updated }, 'expenseUpdated');
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};

export const deleteExpense = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const { id } = req.params;

  try {
    const result = await removeExpense(id, userId!);

    if (result.count === 0) {
      response.notFound(req, res, 'expenseNotFound');
      return;
    }

    response.noContent(res);
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};

export const getInstallments = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { id: userId } = req.user!;

  try {
    const installments = await listInstallments(userId, id);

    if (!installments.length) {
      response.notFound(req, res, 'noInstallments');
      return;
    }

    response.success(req, res, { data: installments });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};
