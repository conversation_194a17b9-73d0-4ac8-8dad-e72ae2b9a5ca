import { Request, Response } from 'express';
import {
  createAccount,
  listAccount,
  listAccounts,
  updateAccount,
  removeAccount,
} from '../services/accountService';
import { response } from '../utils/response';

export const postAccount = async (req: Request, res: Response) => {
  const { name, type, bank, closingDay, dueDay, familyId } = req.body;
  const { id: userId } = req.user!;

  if (!userId || !name || !type || !bank) {
    response.error(req, res, 'missingFields');
    return;
  }

  try {
    const account = await createAccount(userId, {
      name,
      type,
      bank,
      closingDay,
      dueDay,
      familyId,
    });

    response.created(req, res, { account });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'internalServerError', 500);
  }
};

export const getAccounts = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;

  try {
    const accounts = await listAccounts(userId!);
    response.success(req, res, { accounts });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'internalServerError', 500);
  }
};

export const getAccount = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const accountId = req.params.id;

  try {
    const account = await listAccount(userId!, accountId);
    if (!account || account.userId !== userId) {
      response.notFound(req, res, 'accountNotFound');
      return;
    }
    response.success(req, res, account);
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
    } else {
      console.log(err);
    }
    response.error(req, res, 'internalServerError', 500);
  }
};

export const putAccount = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const accountId = req.params.id;
  const data = req.body;

  try {
    const updated = await updateAccount(userId!, accountId, data);
    response.success(req, res, { account: updated });
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
      if (err.message.includes('Record to update not found')) {
        response.notFound(req, res, 'accountNotFound');
        return;
      }
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};

export const deleteAccount = async (req: Request, res: Response) => {
  const { id: userId } = req.user!;
  const accountId = req.params.id;

  try {
    await removeAccount(userId!, accountId);
    response.noContent(res);
  } catch (err) {
    if (err instanceof Error) {
      console.log(err.message);
      if (err.message.includes('Record to delete does not exist')) {
        response.notFound(req, res, 'accountNotFound');
        return;
      }
    } else {
      console.log(err);
    }
    response.error(req, res, 'badRequest');
  }
};
