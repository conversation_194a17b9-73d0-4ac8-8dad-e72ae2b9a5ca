import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { config } from '../config';
import type { Expense } from '../types';

// Types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface ApiError {
  message: string;
  status: number;
  code?: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
}

export interface RegisterResponse {
  user: {
    id: string;
    name: string;
    email: string;
  };
}

// API Client class
class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.api.baseUrl,
      timeout: config.api.timeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (requestConfig) => {
        const token = localStorage.getItem(config.auth.tokenKey);
        if (token) {
          requestConfig.headers.Authorization = `Bearer ${token}`;
        }
        return requestConfig;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response: AxiosResponse) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid
          localStorage.removeItem(config.auth.tokenKey);
          window.location.href = '/login';
        }
        return Promise.reject(this.formatError(error));
      }
    );
  }

  private formatError(error: any): ApiError {
    return {
      message: error.response?.data?.message || error.message || 'An error occurred',
      status: error.response?.status || 0,
      code: error.response?.data?.code,
    };
  }

  // Generic request methods
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.get(url, config);
    return response.data;
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.post(url, data, config);
    return response.data;
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.put(url, data, config);
    return response.data;
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    const response = await this.client.delete(url, config);
    return response.data;
  }

  // Auth methods
  async login(credentials: LoginRequest): Promise<ApiResponse<AuthResponse>> {
    return this.post('/auth/login', credentials);
  }

  async register(userData: RegisterRequest): Promise<ApiResponse<RegisterResponse>> {
    return this.post('/auth/register', userData);
  }

  async logout(): Promise<ApiResponse<void>> {
    return this.post('/auth/logout');
  }

  async refreshToken(): Promise<ApiResponse<{ token: string }>> {
    return this.post('/auth/refresh');
  }

  // Account methods
  async getAccounts(): Promise<ApiResponse<any[]>> {
    return this.get('/accounts');
  }

  async getAccount(id: string): Promise<ApiResponse<any>> {
    return this.get(`/accounts/${id}`);
  }

  async createAccount(accountData: any): Promise<ApiResponse<any>> {
    return this.post('/accounts', accountData);
  }

  async updateAccount(id: string, accountData: any): Promise<ApiResponse<any>> {
    return this.put(`/accounts/${id}`, accountData);
  }

  async deleteAccount(id: string): Promise<ApiResponse<void>> {
    return this.delete(`/accounts/${id}`);
  }

  // Expense methods
  async getExpenses(params?: Record<string, unknown>): Promise<ApiResponse<Expense[]>> {
    return this.get('/expenses', {
      params: {
        ...params,
        _t: Date.now() // Cache buster
      },
      headers: {
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });
  }

  async getExpense(id: string): Promise<ApiResponse<any>> {
    return this.get(`/expenses/${id}`);
  }

  async createExpense(expenseData: any): Promise<ApiResponse<any>> {
    return this.post('/expenses', expenseData);
  }

  async updateExpense(id: string, expenseData: any): Promise<ApiResponse<any>> {
    return this.put(`/expenses/${id}`, expenseData);
  }

  async deleteExpense(id: string): Promise<ApiResponse<void>> {
    return this.delete(`/expenses/${id}`);
  }
}

// Export singleton instance
export const api = new ApiClient();
export default api;
