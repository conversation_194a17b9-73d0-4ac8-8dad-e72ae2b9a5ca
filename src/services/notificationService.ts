import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Generate notification message based on type and data
const generateNotificationMessage = (
  type: 'EXPENSE_DUE' | 'BALANCE_LOW' | 'BUDGET_EXCEEDED',
  data: Record<string, any>,
): string => {
  switch (type) {
    case 'EXPENSE_DUE':
      return `Expense due: ${data.title} - $${data.amount} on ${new Date(data.date).toLocaleDateString()}`;
    case 'BALANCE_LOW':
      return `Low balance alert: Account ${data.accountName} has balance $${data.balance}`;
    case 'BUDGET_EXCEEDED':
      return `Budget exceeded: ${data.category} budget exceeded by $${data.amount}`;
    default:
      return 'New notification';
  }
};

export const createNotification = async (
  userId: string,
  type: 'EXPENSE_DUE' | 'BALANCE_LOW' | 'BUDGET_EXCEEDED',
  data: Record<string, any>,
) => {
  return prisma.notification.create({
    data: {
      userId,
      type,
      message: generateNotificationMessage(type, data),
      read: false,
    },
  });
};

// Schedule notifications for upcoming expenses
export const scheduleExpenseNotifications = async () => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);

  const upcomingExpenses = await prisma.expense.findMany({
    where: {
      date: {
        gte: new Date(),
        lte: tomorrow,
      },
    },
  });

  for (const expense of upcomingExpenses) {
    await createNotification(expense.userId, 'EXPENSE_DUE', {
      expenseId: expense.id,
      title: expense.title,
      amount: expense.amount,
      date: expense.date,
    });
  }
};
