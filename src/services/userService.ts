import { PrismaClient } from '@prisma/client';
import { hashPassword } from '../utils/hash';

const prisma = new PrismaClient();

export const getUserById = async (id: string) => {
  return await prisma.user.findUnique({
    where: { id },
    select: {
      id: true,
      name: true,
      email: true,
      createdAt: true,
    },
  });
};

export const updateUserProfile = async (
  id: string,
  data: {
    name?: string;
    email?: string;
    password?: string;
  },
) => {
  const updateData: any = {
    name: data.name,
    email: data.email,
  };

  if (data.password) {
    updateData.password = await hashPassword(data.password);
  }

  return await prisma.user.update({
    where: { id },
    data: updateData,
    select: {
      id: true,
      name: true,
      email: true,
      createdAt: true,
    },
  });
};
