import { PrismaClient } from '@prisma/client';

type ExpenseUpdateInput = {
  title?: string;
  amount?: number;
  date?: Date;
  category?: string;
  notes?: string;
  accountId?: string;
};

const prisma = new PrismaClient();

export const createExpense = async (data: {
  title: string;
  amount: number;
  category: string;
  accountId: string;
  date: Date;
  notes?: string;
  userId: string;
  familyId?: string;
}) => {
  return await prisma.expense.create({
    data: {
      title: data.title,
      amount: data.amount,
      category: data.category,
      accountId: data.accountId,
      date: data.date,
      notes: data.notes,
      userId: data.userId,
      familyId: data.familyId,
    },
  });
};

export const listExpense = async (userId: string, expenseId: string) => {
  return await prisma.expense.findUnique({
    where: { userId, id: expenseId },
    include: {
      account: {
        select: {
          id: true,
          name: true,
          bank: true,
          type: true,
        },
      },
      installmentInfo: true,
    },
  });
};

export const listExpenses = async ({
  userId,
  familyId,
  accountId,
  startDate,
  endDate,
  category,
  page,
  limit,
}: {
  userId: string;
  familyId?: string;
  accountId?: string;
  startDate?: string;
  endDate?: string;
  category?: string;
  page?: number;
  limit?: number;
}) => {
  const baseQuery = {
    where: {
      userId,
      familyId,
      accountId,
      category: category || undefined,
      date: {
        gte: startDate ? new Date(startDate) : undefined,
        lte: endDate ? new Date(endDate) : undefined,
      },
    },
    orderBy: {
      date: 'desc' as const,
    },
    include: {
      account: {
        select: {
          id: true,
          name: true,
          bank: true,
          type: true,
        },
      },
    },
  };

  // Add pagination if provided
  if (page && limit) {
    return await prisma.expense.findMany({
      ...baseQuery,
      skip: (page - 1) * limit,
      take: limit,
    });
  }

  return await prisma.expense.findMany(baseQuery);
};

export const updateExpense = async (id: string, userId: string, updates: ExpenseUpdateInput) => {
  const existing = await prisma.expense.findUnique({ where: { id } });

  if (!existing || existing.userId !== userId) {
    throw new Error('Not found or unauthorized');
  }

  const updated = await prisma.expense.update({
    where: { id },
    data: updates,
  });

  return updated;
};

export const removeExpense = async (id: string, userId: string) => {
  return await prisma.expense.deleteMany({
    where: { id, userId },
  });
};

export const createInstallments = async (data: {
  userId: string;
  familyId?: string;
  title: string;
  amount: number;
  category: string;
  date: Date;
  accountId: string;
  notes?: string;
  installments: number;
}) => {
  const { userId, familyId, title, amount, category, date, accountId, notes, installments } = data;

  const expensesData = Array.from({ length: installments }).map((_, i) => {
    const current = i + 1;
    const installmentDate = new Date(date);
    installmentDate.setMonth(date.getMonth() + i);

    const value = parseFloat((amount / installments).toFixed(2));

    return {
      title: `${title} (${current}/${installments})`,
      amount: value,
      category,
      date: installmentDate,
      isInstallment: true,
      userId,
      familyId,
      accountId,
      notes,
    };
  });

  const createdExpenses = await prisma.$transaction(
    expensesData.map((expense, i) =>
      prisma.expense.create({
        data: {
          ...expense,
          installmentInfo: {
            create: {
              total: installments,
              current: i + 1,
              startDate: date,
              endDate: new Date(date.getFullYear(), date.getMonth() + installments - 1),
            },
          },
        },
      }),
    ),
  );

  return createdExpenses;
};

export const listInstallments = async (userId: string, expenseId: string) => {
  const base = await prisma.installmentInfo.findUnique({
    where: { expenseId },
    include: {
      expense: {
        include: {
          account: true,
        },
      },
    },
  });

  if (!base || base.expense.userId !== userId) {
    throw new Error('Not found or unauthorized');
  }

  if (!base) return [];

  const expenses = await prisma.expense.findMany({
    where: {
      title: {
        contains: base.expense.title.split(' (')[0], // pega o título base
      },
      userId: userId,
      accountId: base.expense.accountId,
      category: base.expense.category,
      isInstallment: true,
    },
    orderBy: {
      date: 'asc',
    },
  });

  return expenses;
};

export const deleteInstallments = async (id: string, userId: string) => {
  const base = await prisma.installmentInfo.findUnique({
    where: { expenseId: id },
    include: { expense: true },
  });

  if (!base || base.expense.userId !== userId) {
    throw new Error('Not found or unauthorized');
  }

  const { title, accountId, userId: owner } = base.expense;

  await prisma.expense.deleteMany({
    where: {
      title: {
        contains: title.split(' (')[0],
      },
      accountId,
      userId: owner,
      isInstallment: true,
    },
  });
};
