import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const createAccount = async (
  userId: string,
  data: {
    name: string;
    type: 'CREDIT' | 'DEBIT' | 'CHECKING' | 'SAVINGS';
    bank: string;
    closingDay?: number;
    dueDay?: number;
    familyId?: string;
  },
) => {
  const account = await prisma.account.create({
    data: {
      name: data.name,
      type: data.type,
      bank: data.bank,
      userId,
      familyId: data.familyId || null,
      closingDay: data.closingDay || null,
      dueDay: data.dueDay || null,
    },
  });

  return account;
};

export const listAccounts = async (userId: string) => {
  const accounts = await prisma.account.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
  });

  return accounts;
};

export const listAccount = async (_userId: string, accountId: string) => {
  return await prisma.account.findUnique({
    where: { id: accountId },
  });
};

export const updateAccount = async (
  _userId: string,
  accountId: string,
  data: Partial<{
    name: string;
    type: 'CREDIT' | 'DEBIT' | 'CHECKING' | 'SAVINGS';
    bank: string;
    closingDay: number;
    dueDay: number;
  }>,
) => {
  return await prisma.account.update({
    where: { id: accountId },
    data,
  });
};

export const removeAccount = async (_userId: string, accountId: string) => {
  return await prisma.account.delete({
    where: { id: accountId },
  });
};

export const getAccountBalance = async (accountId: string) => {
  const account = await prisma.account.findUnique({
    where: { id: accountId },
  });

  if (!account) throw new Error('Account not found');

  // Get all expenses for this account
  const expenses = await prisma.expense.aggregate({
    where: { accountId },
    _sum: { amount: true },
  });

  // Get all income for this account
  const income = await prisma.income.aggregate({
    where: { accountId },
    _sum: { amount: true },
  });

  const totalExpenses = expenses._sum.amount || 0;
  const totalIncome = income._sum.amount || 0;

  return {
    accountId,
    balance: totalIncome - totalExpenses,
    totalIncome,
    totalExpenses,
  };
};
