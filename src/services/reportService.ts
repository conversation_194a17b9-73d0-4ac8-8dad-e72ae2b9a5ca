import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export const getMonthlySummary = async (userId: string, year: number, month: number) => {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);

  const expenses = await prisma.expense.findMany({
    where: {
      userId,
      date: { gte: startDate, lte: endDate },
    },
    include: {
      account: true,
    },
  });

  // Group by category
  const byCategory = expenses.reduce(
    (acc, expense) => {
      const category = expense.category;
      if (!acc[category]) acc[category] = 0;
      acc[category] += expense.amount;
      return acc;
    },
    {} as Record<string, number>,
  );

  // Total
  const total = expenses.reduce((sum, expense) => sum + expense.amount, 0);

  return {
    total,
    byCategory,
    period: {
      year,
      month,
      startDate,
      endDate,
    },
  };
};
