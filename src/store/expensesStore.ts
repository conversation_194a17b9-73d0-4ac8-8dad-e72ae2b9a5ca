import { create } from 'zustand';
import { api } from '../services/api';
import type { Expense } from '../types/expense';

interface ExpenseFilters {
  accountId?: string;
  startDate?: string;
  endDate?: string;
  category?: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface ExpensesState {
  // Data
  expenses: Expense[];
  
  // Pagination
  pagination: PaginationInfo;
  
  // Filters
  filters: ExpenseFilters;
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchExpenses: (page?: number, filters?: ExpenseFilters) => Promise<void>;
  addExpense: (expenseData: Omit<Expense, 'id' | 'createdAt' | 'account'> & { accountId: string }) => Promise<Expense | null>;
  updateExpense: (id: string, expenseData: Partial<Expense>) => Promise<Expense | null>;
  deleteExpense: (id: string) => Promise<boolean>;
  setPage: (page: number) => Promise<void>;
  setFilters: (filters: ExpenseFilters) => Promise<void>;
  clearFilters: () => Promise<void>;
  clearError: () => void;
  reset: () => void;
}

const initialPagination: PaginationInfo = {
  currentPage: 1,
  totalPages: 1,
  totalItems: 0,
  itemsPerPage: 10,
  hasNextPage: false,
  hasPrevPage: false,
};

const initialState = {
  expenses: [],
  pagination: initialPagination,
  filters: {},
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
};

export const useExpensesStore = create<ExpensesState>((set, get) => ({
  ...initialState,

  fetchExpenses: async (page = 1, newFilters) => {
    try {
      set({ isLoading: true, error: null });

      const currentFilters = newFilters || get().filters;
      const pageToFetch = page;

      console.log('🔍 Fetching expenses from API...', { page: pageToFetch, filters: currentFilters });
      
      const response = await api.getExpenses({
        page: pageToFetch,
        limit: get().pagination.itemsPerPage,
        ...currentFilters,
      });

      let expensesData: Expense[] = [];
      let paginationData = {
        total: 0,
        page: pageToFetch,
        totalPages: 1,
      };

      // Handle different response structures
      if (response.data) {
        if (Array.isArray(response.data)) {
          expensesData = response.data;
          paginationData.total = response.data.length;
        } else if (typeof response.data === 'object') {
          const dataObj = response.data as any;
          if ('data' in dataObj) {
            expensesData = dataObj.data || [];
          }
          if ('pagination' in dataObj) {
            paginationData = { ...paginationData, ...dataObj.pagination };
          } else if ('total' in dataObj) {
            paginationData.total = dataObj.total;
            paginationData.totalPages = Math.ceil(dataObj.total / get().pagination.itemsPerPage);
          }
        }
      }

      // Sort expenses by date (newest first)
      const sortedExpenses = expensesData.sort((a: Expense, b: Expense) =>
        new Date(b.date).getTime() - new Date(a.date).getTime()
      );

      const totalPages = Math.ceil(paginationData.total / get().pagination.itemsPerPage);

      set({
        expenses: sortedExpenses,
        pagination: {
          currentPage: pageToFetch,
          totalPages,
          totalItems: paginationData.total,
          itemsPerPage: get().pagination.itemsPerPage,
          hasNextPage: pageToFetch < totalPages,
          hasPrevPage: pageToFetch > 1,
        },
        filters: currentFilters,
        isLoading: false,
      });

      console.log('✅ Expenses loaded successfully:', sortedExpenses.length, 'items, page', pageToFetch);
    } catch (error) {
      console.error('❌ Error fetching expenses:', error);
      const errorMessage = (error as Error).message || 'Failed to fetch expenses';
      set({ error: errorMessage, isLoading: false });
    }
  },

  addExpense: async (expenseData) => {
    try {
      set({ isCreating: true, error: null });

      console.log('➕ Creating expense:', expenseData);
      const response = await api.createExpense(expenseData);

      let newExpense: Expense | null = null;
      if (response.data) {
        if (typeof response.data === 'object' && 'id' in response.data) {
          newExpense = response.data as Expense;
        } else if (typeof response.data === 'object' && 'data' in response.data) {
          newExpense = (response.data as { data: Expense }).data;
        }
      }

      if (newExpense) {
        // Refresh the expenses list to get updated data
        await get().fetchExpenses(get().pagination.currentPage, get().filters);
        set({ isCreating: false });
        console.log('✅ Expense created successfully:', newExpense);
        return newExpense;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error creating expense:', error);
      const errorMessage = (error as Error).message || 'Failed to create expense';
      set({ error: errorMessage, isCreating: false });
      return null;
    }
  },

  updateExpense: async (id, expenseData) => {
    try {
      set({ isUpdating: true, error: null });

      console.log('📝 Updating expense:', id, expenseData);
      const response = await api.updateExpense(id, expenseData);

      let updatedExpense: Expense | null = null;
      if (response.data) {
        if (typeof response.data === 'object' && 'id' in response.data) {
          updatedExpense = response.data as Expense;
        } else if (typeof response.data === 'object' && 'data' in response.data) {
          updatedExpense = (response.data as { data: Expense }).data;
        }
      }

      if (updatedExpense) {
        set(state => ({
          expenses: state.expenses.map(expense => 
            expense.id === id ? updatedExpense! : expense
          ),
          isUpdating: false
        }));
        console.log('✅ Expense updated successfully:', updatedExpense);
        return updatedExpense;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error updating expense:', error);
      const errorMessage = (error as Error).message || 'Failed to update expense';
      set({ error: errorMessage, isUpdating: false });
      return null;
    }
  },

  deleteExpense: async (id) => {
    try {
      set({ isDeleting: true, error: null });

      console.log('🗑️ Deleting expense:', id);
      await api.deleteExpense(id);

      set(state => ({
        expenses: state.expenses.filter(expense => expense.id !== id),
        isDeleting: false
      }));
      console.log('✅ Expense deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ Error deleting expense:', error);
      const errorMessage = (error as Error).message || 'Failed to delete expense';
      set({ error: errorMessage, isDeleting: false });
      return false;
    }
  },

  setPage: async (page) => {
    if (page >= 1 && page <= get().pagination.totalPages) {
      await get().fetchExpenses(page, get().filters);
    }
  },

  setFilters: async (newFilters) => {
    await get().fetchExpenses(1, newFilters); // Reset to page 1 when filters change
  },

  clearFilters: async () => {
    await get().fetchExpenses(1, {}); // Reset to page 1 with no filters
  },

  clearError: () => set({ error: null }),

  reset: () => set(initialState),
}));
