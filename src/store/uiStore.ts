import { create } from 'zustand';

interface UIState {
  // Sidebar
  sidebarOpen: boolean;
  setSidebarOpen: (open: boolean) => void;
  toggleSidebar: () => void;

  // Modals
  activeModal: string | null;
  modalData: Record<string, any>;
  openModal: (modalId: string, data?: any) => void;
  closeModal: () => void;

  // Notifications/Toast
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id'>) => void;
  removeNotification: (id: string) => void;
  clearNotifications: () => void;

  // Loading states
  globalLoading: boolean;
  setGlobalLoading: (loading: boolean) => void;
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message?: string;
  duration?: number;
}

export const useUIStore = create<UIState>((set, get) => ({
  // Sidebar
  sidebarOpen: false,
  setSidebarOpen: (open: boolean) => set({ sidebarOpen: open }),
  toggleSidebar: () => set({ sidebarOpen: !get().sidebarOpen }),

  // Modals
  activeModal: null,
  modalData: {},
  openModal: (modalId: string, data?: any) =>
    set({ activeModal: modalId, modalData: data || {} }),
  closeModal: () => set({ activeModal: null, modalData: {} }),

  // Notifications
  notifications: [],
  addNotification: (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString();
    const newNotification = { ...notification, id };
    set({ notifications: [...get().notifications, newNotification] });

    // Auto-remove after duration
    if (notification.duration !== -1) {
      setTimeout(() => {
        get().removeNotification(id);
      }, notification.duration || 5000);
    }
  },
  removeNotification: (id: string) =>
    set({ notifications: get().notifications.filter(n => n.id !== id) }),
  clearNotifications: () => set({ notifications: [] }),

  // Loading
  globalLoading: false,
  setGlobalLoading: (loading: boolean) => set({ globalLoading: loading }),
}));
