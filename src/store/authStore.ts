import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { api } from '../services/api';
import type { LoginRequest, RegisterRequest } from '../services/api';

export interface User {
  id: string;
  name: string;
  email: string;
  isProUser: boolean;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  setUser: (user: User) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await api.login(credentials);
          const { token } = response.data;

          // Store token in localStorage
          localStorage.setItem('granik_auth_token', token);

          // Decode user info from JWT token
          const payload = JSON.parse(atob(token.split('.')[1]));
          const user = {
            id: payload.userId,
            email: payload.email,
            name: payload.email.split('@')[0], // Fallback until we get actual name
            isProUser: false, // Default value
          };

          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Login failed',
          });
          throw error;
        }
      },

      register: async (userData: RegisterRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await api.register(userData);
          const { user } = response.data;

          // For register, we get user info directly but need to login separately
          // This matches the backend that returns user info on register
          set({
            user: {
              ...user,
              isProUser: false, // Default value
            },
            token: null, // No token from register
            isAuthenticated: false, // Not authenticated until login
            isLoading: false,
            error: null,
          });
        } catch (error: unknown) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Registration failed',
          });
          throw error;
        }
      },

      logout: () => {
        localStorage.removeItem('granik_auth_token');
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        });
      },

      clearError: () => set({ error: null }),

      setUser: (user: User) => set({ user }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
