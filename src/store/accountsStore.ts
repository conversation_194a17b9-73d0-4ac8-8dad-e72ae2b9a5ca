import { create } from 'zustand';
import { api } from '../services/api';
import type { Account } from '../types/expense';

interface AccountsState {
  // Data
  accounts: Account[];
  
  // Loading states
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchAccounts: () => Promise<void>;
  addAccount: (accountData: Omit<Account, 'id' | 'createdAt' | 'updatedAt'>) => Promise<Account | null>;
  updateAccount: (id: string, accountData: Partial<Account>) => Promise<Account | null>;
  deleteAccount: (id: string) => Promise<boolean>;
  clearError: () => void;
  reset: () => void;
}

const initialState = {
  accounts: [],
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  error: null,
};

export const useAccountsStore = create<AccountsState>((set, get) => ({
  ...initialState,

  fetchAccounts: async () => {
    try {
      set({ isLoading: true, error: null });

      console.log('🔍 Fetching accounts from API...');
      const response = await api.getAccounts();

      let accountsData: Account[] = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          accountsData = response.data;
        } else if (typeof response.data === 'object' && 'data' in response.data) {
          accountsData = (response.data as { data: Account[] }).data || [];
        }
      }

      set({ accounts: accountsData, isLoading: false });
      console.log('✅ Accounts loaded successfully:', accountsData.length, 'items');
    } catch (error) {
      console.error('❌ Error fetching accounts:', error);
      const errorMessage = (error as Error).message || 'Failed to fetch accounts';
      set({ error: errorMessage, isLoading: false });
    }
  },

  addAccount: async (accountData) => {
    try {
      set({ isCreating: true, error: null });

      console.log('➕ Creating account:', accountData);
      const response = await api.createAccount(accountData);

      let newAccount: Account | null = null;
      if (response.data) {
        if (typeof response.data === 'object' && 'id' in response.data) {
          newAccount = response.data as Account;
        } else if (typeof response.data === 'object' && 'data' in response.data) {
          newAccount = (response.data as { data: Account }).data;
        }
      }

      if (newAccount) {
        set(state => ({
          accounts: [...state.accounts, newAccount!],
          isCreating: false
        }));
        console.log('✅ Account created successfully:', newAccount);
        return newAccount;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error creating account:', error);
      const errorMessage = (error as Error).message || 'Failed to create account';
      set({ error: errorMessage, isCreating: false });
      return null;
    }
  },

  updateAccount: async (id, accountData) => {
    try {
      set({ isUpdating: true, error: null });

      console.log('📝 Updating account:', id, accountData);
      const response = await api.updateAccount(id, accountData);

      let updatedAccount: Account | null = null;
      if (response.data) {
        if (typeof response.data === 'object' && 'id' in response.data) {
          updatedAccount = response.data as Account;
        } else if (typeof response.data === 'object' && 'data' in response.data) {
          updatedAccount = (response.data as { data: Account }).data;
        }
      }

      if (updatedAccount) {
        set(state => ({
          accounts: state.accounts.map(account => 
            account.id === id ? updatedAccount! : account
          ),
          isUpdating: false
        }));
        console.log('✅ Account updated successfully:', updatedAccount);
        return updatedAccount;
      } else {
        throw new Error('Invalid response format');
      }
    } catch (error) {
      console.error('❌ Error updating account:', error);
      const errorMessage = (error as Error).message || 'Failed to update account';
      set({ error: errorMessage, isUpdating: false });
      return null;
    }
  },

  deleteAccount: async (id) => {
    try {
      set({ isDeleting: true, error: null });

      console.log('🗑️ Deleting account:', id);
      await api.deleteAccount(id);

      set(state => ({
        accounts: state.accounts.filter(account => account.id !== id),
        isDeleting: false
      }));
      console.log('✅ Account deleted successfully');
      return true;
    } catch (error) {
      console.error('❌ Error deleting account:', error);
      const errorMessage = (error as Error).message || 'Failed to delete account';
      set({ error: errorMessage, isDeleting: false });
      return false;
    }
  },

  clearError: () => set({ error: null }),

  reset: () => set(initialState),
}));
