import en from './lang/en_us';
import pt from './lang/pt_br';
import type { MessageKey } from './messageKeys';
import type { Locale } from '../../@types/express';

const translations: Record<Locale, Record<MessageKey, string>> = {
  en,
  pt,
};

export const t = (key: MessageKey, locale?: Locale): string => {
  const selectedLocale = locale || 'en';
  return translations[selectedLocale]?.[key] ?? key;
};

export type { Locale } from '../../@types/express';
