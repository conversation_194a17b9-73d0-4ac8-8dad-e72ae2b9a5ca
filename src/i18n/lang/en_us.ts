import type { MessageKey } from '../messageKeys';

const en_us: Record<MessageKey, string> = {
  ok: 'OK',
  missingFields: 'Missing required fields',
  internalServerError: 'Internal server error',
  accountNotFound: 'Account not found',
  badRequest: 'Bad request',
  missingCredentials: 'Missing credentials',
  unauthorized: 'Unauthorized',
  invalidToken: 'Missing or invalid token',
  expiredToken: 'Invalid or expired token',
  expenseNotFound: 'Expense not found',
  createdInstallments: 'Installment expenses created',
  expenseCreated: 'Expense created',
  expenseUpdated: 'Expense updated',
  expenseDeleted: 'Expense deleted successfully',
  noInstallments: 'No installments found for this expense',
  notFound: 'Not found',
  invalidRequest: 'Invalid request',
  validationError: 'Validation error',
};

export default en_us;
