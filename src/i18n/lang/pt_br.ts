import type { MessageKey } from '../messageKeys';

const pt_br: Record<MessageKey, string> = {
  ok: '<PERSON>do certo',
  missingFields: 'Campos obrigatórios ausentes',
  internalServerError: 'Erro interno do servidor',
  accountNotFound: 'Conta não encontrada',
  badRequest: 'Requisição inválida',
  missingCredentials: 'Credenciais ausentes',
  unauthorized: 'Não autorizado',
  invalidToken: 'Token ausente ou inválido',
  expiredToken: 'Token inválido ou expirado',
  expenseNotFound: 'Despesa não encontrada',
  createdInstallments: 'Parcelas da despesa criadas',
  expenseCreated: 'Despesa criada',
  expenseUpdated: 'Despesa atualizada',
  expenseDeleted: 'Despesa excluída com sucesso',
  noInstallments: 'Nenhuma parcela encontrada para essa despesa',
  notFound: 'Não encontrado',
  invalidRequest: '<PERSON>quis<PERSON><PERSON> inválida',
  validationError: 'Erro de validação',
};

export default pt_br;
