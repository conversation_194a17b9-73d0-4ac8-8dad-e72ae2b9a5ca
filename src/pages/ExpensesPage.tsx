import React from "react";
import { useExpenses } from "../hooks/useExpenses";
import { expenseCategories } from "../config";
import { runSampleDataScript } from "../utils/addSampleData";

const ExpensesPage: React.FC = () => {
  const { expenses, isLoading, error, refetch } = useExpenses();

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    }).format(new Date(dateString));
  };

  const getCategoryLabel = (category: string) => {
    const categoryConfig = expenseCategories.find(
      (cat) => cat.value === category
    );
    return categoryConfig?.label || category;
  };

  const handleAddSampleData = async () => {
    try {
      await runSampleDataScript();
      // Refresh the expenses list after adding sample data
      await refetch();
    } catch (error) {
      console.error("Error adding sample data:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
          Expenses
        </h1>
        <p className="mt-1 text-sm text-gray-500">
          Track and manage your expenses
        </p>
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 text-sm p-4 rounded-xl">
          {error}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && !error && expenses.length === 0 && (
        <div className="bg-white rounded-xl shadow-sm p-8">
          <div className="text-center py-12">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
            </div>
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No expenses found
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding your first expense.
            </p>
            <div className="mt-6 space-y-3">
              <button
                onClick={handleAddSampleData}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-xl text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Add Sample Data
              </button>
              <p className="text-xs text-gray-400">
                This will create sample accounts and expenses for testing
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Expenses List */}
      {!isLoading && !error && expenses.length > 0 && (
        <div className="space-y-4">
          {expenses.map((expense) => (
            <div
              key={expense.id}
              className="bg-white rounded-xl shadow-sm p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900">
                      {expense.title}
                    </h3>
                    <span className="text-lg font-semibold text-gray-900">
                      {formatCurrency(expense.amount)}
                    </span>
                  </div>
                  <div className="mt-1 flex items-center space-x-4 text-sm text-gray-600">
                    <span>{formatDate(expense.date)}</span>
                    <span>•</span>
                    <span>{getCategoryLabel(expense.category)}</span>
                    <span>•</span>
                    <span>{expense.account.name}</span>
                  </div>
                  {expense.notes && (
                    <p className="mt-2 text-sm text-gray-600">
                      {expense.notes}
                    </p>
                  )}
                  {expense.installmentInfo && (
                    <div className="mt-2 text-xs text-gray-500">
                      Installment {expense.installmentInfo.current} of{" "}
                      {expense.installmentInfo.total}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ExpensesPage;
