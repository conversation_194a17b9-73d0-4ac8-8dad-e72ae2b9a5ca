import React from "react";

const FamiliesPage: React.FC = () => {
  return (
    <div className="space-y-6 p-6">
      <div className="border-b border-gray-200 pb-4">
        <h1 className="text-2xl font-bold text-gray-900">Families</h1>
        <p className="mt-1 text-sm text-gray-500">
          Manage your family financial sharing and collaboration.
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <p className="text-gray-600">
          Family management features are coming soon! This will allow you to:
        </p>
        <ul className="mt-4 space-y-2 text-sm text-gray-600">
          <li className="flex items-center">
            <span className="h-1 w-1 bg-gray-400 rounded-full mr-2"></span>
            Share expenses with family members
          </li>
          <li className="flex items-center">
            <span className="h-1 w-1 bg-gray-400 rounded-full mr-2"></span>
            Manage family budgets
          </li>
          <li className="flex items-center">
            <span className="h-1 w-1 bg-gray-400 rounded-full mr-2"></span>
            Track shared accounts
          </li>
          <li className="flex items-center">
            <span className="h-1 w-1 bg-gray-400 rounded-full mr-2"></span>
            Set spending permissions
          </li>
        </ul>
      </div>
    </div>
  );
};

export default FamiliesPage;
