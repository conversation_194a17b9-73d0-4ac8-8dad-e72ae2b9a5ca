import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { useAuth } from "./hooks/useAuth";
import { Layout, Toast } from "./components";
import {
  LoginPage,
  DashboardPage,
  ExpensesPage,
  AccountsPage,
  FamiliesPage,
} from "./pages";
import { routes } from "./config";

// Protected Route Component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { isAuthenticated } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to={routes.login} replace />;
  }

  return <>{children}</>;
};

// Public Route Component (redirect if authenticated)
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return <Navigate to={routes.dashboard} replace />;
  }

  return <>{children}</>;
};

function App() {
  return (
    <Router>
      <div className="App">
        <Toast />
        <Routes>
          {/* Public Routes */}
          <Route
            path={routes.login}
            element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            }
          />

          {/* Protected Routes */}
          <Route
            path="/"
            element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }
          >
            <Route index element={<Navigate to={routes.dashboard} replace />} />
            <Route
              path={routes.dashboard.slice(1)}
              element={<DashboardPage />}
            />
            <Route path={routes.expenses.slice(1)} element={<ExpensesPage />} />
            <Route path={routes.accounts.slice(1)} element={<AccountsPage />} />
            <Route path={routes.families.slice(1)} element={<FamiliesPage />} />
            {/* Add more routes here */}
          </Route>

          {/* Catch all route */}
          <Route
            path="*"
            element={<Navigate to={routes.dashboard} replace />}
          />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
