export interface Account {
  id: string;
  name: string;
  bank: string;
  type: string;
  balance?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface Expense {
  id: string;
  title: string;
  amount: number;
  category: string;
  date: string;
  notes?: string;
  account: Account;
  installmentInfo?: {
    id: string;
    total: number;
    current: number;
    startDate: string;
  };
  createdAt: string;
}
