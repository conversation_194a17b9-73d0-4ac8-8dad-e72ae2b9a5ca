import { Request, Response } from 'express';
import { t } from '../i18n';
import { MessageKey } from 'src/i18n/messageKeys';

type Payload = Record<string, unknown> | null;

const build = (
  req: Request,
  res: Response,
  statusCode: number,
  payload: Payload = null,
  messageKey?: MessageKey,
) => {
  const response: Record<string, unknown> = {};

  if (messageKey) {
    response.message = t(messageKey, req.user?.locale);
  }

  if (payload) {
    response.data = payload;
  }

  return res.status(statusCode).json(response);
};

const buildError = (req: Request, res: Response, statusCode: number, messageKey: MessageKey) => {
  return res.status(statusCode).json({
    error: t(messageKey, req.user?.locale),
  });
};

export const response = {
  success: (req: Request, res: Response, payload: Payload = null, messageKey?: MessageKey) =>
    build(req, res, 200, payload, messageKey),

  created: (req: Request, res: Response, payload: Payload = null, messageKey?: MessageKey) =>
    build(req, res, 201, payload, messageKey),

  error: (req: Request, res: Response, messageKey: MessageKey, statusCode = 400) =>
    buildError(req, res, statusCode, messageKey),

  notFound: (req: Request, res: Response, messageKey: MessageKey = 'notFound') =>
    buildError(req, res, 404, messageKey),

  unauthorized: (req: Request, res: Response, messageKey: MessageKey = 'unauthorized') =>
    buildError(req, res, 401, messageKey),

  noContent: (res: Response) => res.status(204).send(),
};
