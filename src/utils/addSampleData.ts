// Utility script to add sample data via API
// Run this script to populate your database with sample accounts and expenses

import { api } from '../services/api';

const sampleAccounts = [
  {
    name: 'Main Checking',
    type: 'CHECKING',
    bank: 'Chase Bank',
  },
  {
    name: 'Savings Account',
    type: 'SAVINGS',
    bank: 'Chase Bank',
  },
  {
    name: 'Credit Card',
    type: 'CREDIT',
    bank: 'Bank of America',
  },
];

const sampleExpenses = [
  {
    title: 'Grocery Shopping',
    amount: 89.45,
    category: 'FOOD',
    date: new Date('2024-07-19').toISOString(),
    notes: 'Weekly grocery shopping at Whole Foods',
  },
  {
    title: 'Gas Station',
    amount: 52.20,
    category: 'TRANSPORT',
    date: new Date('2024-07-18').toISOString(),
  },
  {
    title: 'Netflix Subscription',
    amount: 15.99,
    category: 'ENTERTAINMENT',
    date: new Date('2024-07-17').toISOString(),
    notes: 'Monthly subscription',
  },
  {
    title: 'Coffee Shop',
    amount: 4.75,
    category: 'FOOD',
    date: new Date('2024-07-16').toISOString(),
  },
  {
    title: 'Laptop Purchase',
    amount: 1299.99,
    category: 'SHOPPING',
    date: new Date('2024-07-15').toISOString(),
    notes: 'MacBook Pro for work',
  },
  {
    title: 'Electric Bill',
    amount: 127.45,
    category: 'BILLS',
    date: new Date('2024-07-14').toISOString(),
    notes: 'Monthly electric bill',
  },
  {
    title: 'Restaurant Dinner',
    amount: 68.30,
    category: 'FOOD',
    date: new Date('2024-07-13').toISOString(),
    notes: 'Dinner with friends',
  },
];

export const addSampleData = async () => {
  try {
    console.log('Adding sample accounts...');

    // Create accounts first
    const createdAccounts = [];
    for (const account of sampleAccounts) {
      try {
        const response = await api.createAccount(account);
        if (response.success) {
          createdAccounts.push(response.data);
          console.log(`✅ Created account: ${account.name}`);
        } else {
          console.log(`❌ Failed to create account ${account.name}: ${response.message}`);
        }
      } catch (error) {
        console.log(`❌ Error creating account ${account.name}:`, error);
      }
    }

    if (createdAccounts.length === 0) {
      console.log('❌ No accounts were created. Cannot create expenses.');
      return;
    }

    console.log('\nAdding sample expenses...');

    // Create expenses using the first account
    const primaryAccount = createdAccounts[0];
    const creditAccount = createdAccounts.find(acc => acc.type === 'CREDIT') || primaryAccount;

    for (let i = 0; i < sampleExpenses.length; i++) {
      const expense = sampleExpenses[i];
      const accountToUse = expense.category === 'ENTERTAINMENT' || expense.category === 'SHOPPING'
        ? creditAccount
        : primaryAccount;

      const expenseData = {
        ...expense,
        accountId: accountToUse.id,
      };

      try {
        const response = await api.createExpense(expenseData);
        if (response.success) {
          console.log(`✅ Created expense: ${expense.title}`);
        } else {
          console.log(`❌ Failed to create expense ${expense.title}: ${response.message}`);
        }
      } catch (error) {
        console.log(`❌ Error creating expense ${expense.title}:`, error);
      }
    }

    console.log('\n✅ Sample data creation completed!');

  } catch (error) {
    console.error('❌ Error adding sample data:', error);
  }
};

// Function to check if user is authenticated
export const checkAuth = () => {
  const token = localStorage.getItem('granik_auth_token');
  if (!token) {
    console.log('❌ No authentication token found. Please log in first.');
    return false;
  }
  return true;
};

// Main function to run the script
export const runSampleDataScript = async () => {
  if (!checkAuth()) {
    return;
  }

  console.log('🚀 Starting sample data creation...\n');
  await addSampleData();
};
