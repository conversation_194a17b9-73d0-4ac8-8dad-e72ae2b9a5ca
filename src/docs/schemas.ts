export const AccountSchemas = {
  Account: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
      name: { type: 'string', example: 'Nubank' },
      type: { type: 'string', example: 'credit_card' },
      balance: { type: 'number', example: 1250.5 },
      userId: { type: 'string', format: 'uuid' },
    },
    required: ['name', 'type'],
  },
  AccountInput: {
    type: 'object',
    properties: {
      name: { type: 'string', example: 'Nubank' },
      type: { type: 'string', example: 'credit_card' },
      balance: { type: 'number', example: 0 },
    },
    required: ['name', 'type'],
  },
};

export const AuthSchemas = {
  RegisterInput: {
    type: 'object',
    properties: {
      name: { type: 'string', example: 'Jane <PERSON>' },
      email: { type: 'string', format: 'email', example: '<EMAIL>' },
      password: { type: 'string', example: 'securePass123' },
    },
    required: ['name', 'email', 'password'],
  },
  LoginInput: {
    type: 'object',
    properties: {
      email: { type: 'string', format: 'email', example: '<EMAIL>' },
      password: { type: 'string', example: 'securePass123' },
    },
    required: ['email', 'password'],
  },
  User: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
      name: { type: 'string', example: 'Jane Doe' },
      email: { type: 'string', example: '<EMAIL>' },
    },
  },
};

export const ExpenseSchemas = {
  Expense: {
    type: 'object',
    properties: {
      id: { type: 'string', format: 'uuid' },
      title: { type: 'string', example: 'Aluguel' },
      amount: { type: 'number', example: 1200.52 },
      date: { type: 'string', format: 'date', example: '2025-04-10' },
      category: { type: 'string', example: 'Housing' },
      type: { type: 'string', example: 'fixed' },
      accountId: { type: 'string', format: 'uuid' },
      userId: { type: 'string', format: 'uuid' },
      familyId: { type: 'string', format: 'uuid' },
      notes: { type: 'string', example: 'Aluguel do apartamento' },
      createdAt: { type: 'string', format: 'date-time' },
      updatedAt: { type: 'string', format: 'date-time' },
    },
    required: ['title', 'amount', 'date', 'accountId'],
  },
  ExpenseInput: {
    type: 'object',
    properties: {
      title: { type: 'string', example: 'Aluguel' },
      amount: { type: 'number', example: 1200.52 },
      date: { type: 'string', format: 'date', example: '2025-04-10' },
      category: { type: 'string', example: 'Housing' },
      type: { type: 'string', example: 'fixed' },
      accountId: { type: 'string', format: 'uuid' },
      notes: { type: 'string', example: 'Aluguel do mês de abril' },
      installments: { type: 'number', example: 1 },
    },
    required: ['title', 'amount', 'date', 'accountId'],
  },
  InstallmentList: {
    type: 'object',
    properties: {
      data: {
        type: 'array',
        items: { $ref: '#/components/schemas/Expense' },
      },
    },
  },
};
