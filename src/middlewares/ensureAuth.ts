import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { response } from '../utils/response';

interface TokenPayload {
  userId: string;
  email: string;
  locale?: string;
  iat: number;
  exp: number;
}

export const ensureAuth = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    response.unauthorized(req, res, 'invalidToken');
    return;
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as TokenPayload;
    req.user = {
      id: decoded.userId,
      email: decoded.email,
      locale: 'en', // Default locale, can be updated by setLocale middleware
    };
    next();
  } catch {
    response.unauthorized(req, res, 'expiredToken');
  }
};
