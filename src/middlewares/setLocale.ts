import { Request, Response, NextFunction } from 'express';
import { Locale } from '../../@types/express';

export const setLocale = (req: Request, _res: Response, next: NextFunction) => {
  const lang = req.headers['accept-language']?.split(',')[0] || 'en';
  const locale: Locale = (['en', 'pt'] as Locale[]).includes(lang as Locale)
    ? (lang as Locale)
    : 'en';

  // Update user locale if user exists
  if (req.user) {
    req.user.locale = locale;
  }

  next();
};
