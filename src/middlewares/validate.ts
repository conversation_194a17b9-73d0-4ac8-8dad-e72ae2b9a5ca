import { ZodSchema } from 'zod';
import { Request, Response, NextFunction } from 'express';
import { t } from '../i18n';
import { MessageKey } from '../i18n/messageKeys';

export const validate = (schema: ZodSchema<unknown>) => {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    const result = schema.safeParse(req.body);
    if (!result.success) {
      const messageKey: MessageKey = 'validationError';
      res.status(400).json({ error: t(messageKey, req.user?.locale) });
      return;
    }
    req.body = result.data;
    next();
  };
};
