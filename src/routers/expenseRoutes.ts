import { Router } from 'express';
import {
  getExpenses,
  getExpense,
  postExpense,
  putExpense,
  deleteExpense,
  getInstallments,
} from '../controllers/expenseController';
import { ensureAuth } from '../middlewares/ensureAuth';
import { validate } from '../middlewares/validate';
import { createExpenseSchema, updateExpenseSchema } from '../validations/expenseValidation';

const router = Router();

/**
 * @swagger
 * /expenses:
 *   get:
 *     summary: Get all expenses for the authenticated user
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of expenses
 *       401:
 *         description: Unauthorized
 */
router.get('/', ensureAuth, getExpenses);

/**
 * @swagger
 * /expenses/{id}:
 *   get:
 *     summary: Get a single expense by ID
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Expense ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Expense found
 *       404:
 *         description: Expense not found
 */

router.get('/:id', ensureAuth, getExpense);

/**
 * @swagger
 * /expenses:
 *   post:
 *     summary: Create a new expense
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [notes, amount, date, accountId]
 *             properties:
 *               notes:
 *                 type: string
 *               amount:
 *                 type: number
 *               date:
 *                 type: string
 *                 format: date
 *               accountId:
 *                 type: string
 *               category:
 *                 type: string
 *               installments:
 *                 type: number
 *     responses:
 *       201:
 *         description: Expense created
 *       400:
 *         description: Invalid input
 */
router.post('/', ensureAuth, validate(createExpenseSchema), postExpense);

/**
 * @swagger
 * /expenses/{id}:
 *   put:
 *     summary: Update an existing expense
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Expense ID
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notes:
 *                 type: string
 *               amount:
 *                 type: number
 *               date:
 *                 type: string
 *                 format: date
 *               category:
 *                 type: string
 *     responses:
 *       200:
 *         description: Expense updated
 *       404:
 *         description: Expense not found
 */
router.put('/:id', ensureAuth, validate(updateExpenseSchema), putExpense);

/**
 * @swagger
 * /expenses/{id}:
 *   delete:
 *     summary: Delete an expense
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Expense ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Expense deleted
 *       404:
 *         description: Expense not found or unauthorized
 */
router.delete('/:id', ensureAuth, deleteExpense);

/**
 * @swagger
 * /expenses/{id}/installments:
 *   get:
 *     summary: Get all installments for an expense
 *     tags: [Expenses]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: Expense ID
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Installments list
 *       404:
 *         description: Expense not found
 */
router.get('/:id/installments', ensureAuth, getInstallments);

export default router;
