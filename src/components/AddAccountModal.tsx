import React, { useState } from "react";
import { useUIStore, useAccountsStore } from "../store";
import { accountTypes } from "../config";

interface AddAccountModalProps {
  onSuccess: () => void;
}

interface FormData {
  name: string;
  bank: string;
  type: string;
  balance: string;
}

interface FormErrors {
  name?: string;
  bank?: string;
  type?: string;
  balance?: string;
  general?: string;
}

const AddAccountModal: React.FC<AddAccountModalProps> = ({ onSuccess }) => {
  // Global stores
  const { closeModal, addNotification } = useUIStore();
  const { addAccount, isCreating: isSubmitting } = useAccountsStore();

  // Local state
  const [errors, setErrors] = useState<FormErrors>({});
  const [formData, setFormData] = useState<FormData>({
    name: "",
    bank: "",
    type: "CHECKING",
    balance: "",
  });

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = "Account name is required";
    }

    if (!formData.bank.trim()) {
      newErrors.bank = "Bank name is required";
    }

    if (!formData.type) {
      newErrors.type = "Account type is required";
    }

    if (formData.balance && isNaN(parseFloat(formData.balance))) {
      newErrors.balance = "Balance must be a valid number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setErrors({});

    const accountData = {
      name: formData.name.trim(),
      bank: formData.bank.trim(),
      type: formData.type,
      balance: formData.balance ? parseFloat(formData.balance) : undefined,
    };

    const result = await addAccount(accountData);

    if (result) {
      addNotification({
        type: "success",
        title: "Success",
        message: "Account created successfully",
      });

      // Reset form
      setFormData({
        name: "",
        bank: "",
        type: "CHECKING",
        balance: "",
      });

      closeModal();
      onSuccess();
    } else {
      setErrors({ general: "Failed to create account. Please try again." });
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Add Account</h2>
          <button
            onClick={closeModal}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* General Error */}
          {errors.general && (
            <div className="bg-red-50 border border-red-200 text-red-600 text-sm p-3 rounded-lg">
              {errors.general}
            </div>
          )}

          {/* Account Name */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              Account Name *
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter account name"
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* Bank Name */}
          <div>
            <label htmlFor="bank" className="block text-sm font-medium text-gray-700 mb-1">
              Bank Name *
            </label>
            <input
              type="text"
              id="bank"
              value={formData.bank}
              onChange={(e) => handleInputChange("bank", e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.bank ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter bank name"
            />
            {errors.bank && (
              <p className="mt-1 text-sm text-red-600">{errors.bank}</p>
            )}
          </div>

          {/* Account Type */}
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">
              Account Type *
            </label>
            <select
              id="type"
              value={formData.type}
              onChange={(e) => handleInputChange("type", e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.type ? "border-red-300" : "border-gray-300"
              }`}
            >
              {accountTypes.map((type) => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
            {errors.type && (
              <p className="mt-1 text-sm text-red-600">{errors.type}</p>
            )}
          </div>

          {/* Balance (Optional) */}
          <div>
            <label htmlFor="balance" className="block text-sm font-medium text-gray-700 mb-1">
              Initial Balance (Optional)
            </label>
            <input
              type="number"
              id="balance"
              step="0.01"
              value={formData.balance}
              onChange={(e) => handleInputChange("balance", e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.balance ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="0.00"
            />
            {errors.balance && (
              <p className="mt-1 text-sm text-red-600">{errors.balance}</p>
            )}
          </div>

          {/* Submit Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={closeModal}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Creating...
                </>
              ) : (
                "Create Account"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddAccountModal;
