import { z } from 'zod';

export const createAccountSchema = z.object({
  name: z.string().min(1, 'nameRequired'),
  balance: z.number().optional(),
  type: z.string().min(1, 'typeRequired'),
  bank: z.string().min(1, 'bankRequired'),
  familyId: z.string().uuid('invalidFamilyId').optional(),
  closingDay: z.number().optional(),
  dueDay: z.number().optional(),
});

export const updateAccountSchema = createAccountSchema.partial();
