import { z } from 'zod';

export const registerSchema = z.object({
  name: z.string().min(1, 'nameRequired'),
  email: z.string().email('invalidEmail'),
  password: z.string().min(6, 'passwordTooShort'),
  familyName: z.string().optional(),
});

export const loginSchema = z.object({
  email: z.string().email('invalidEmail'),
  password: z.string().min(1, 'missingPassword'),
});

export const updateProfileSchema = z.object({
  name: z.string().min(1, 'nameRequired').optional(),
  email: z.string().email('invalidEmail').optional(),
});
