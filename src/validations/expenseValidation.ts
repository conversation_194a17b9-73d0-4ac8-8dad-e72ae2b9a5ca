import { z } from 'zod';

export const createExpenseSchema = z.object({
  title: z.string().min(1, 'titleRequired'),
  amount: z.number().positive('amountMustBePositive'),
  accountId: z.string().uuid('invalidAccountId'),
  category: z.string().optional(),
  date: z.string().datetime({ message: 'invalidDate' }),
  notes: z.string().optional(),
  familyId: z.string().uuid('invalidFamilyId').optional().nullable(),
  installments: z.number().min(1, 'installmentsMustBePositive').optional(),
  isInstallment: z.boolean().optional(),
});

export const updateExpenseSchema = createExpenseSchema.partial();

export const createInstallmentsSchema = z.object({
  title: z.string().min(1, 'titleRequired'),
  amount: z.number().positive('amountMustBePositive'),
  accountId: z.string().uuid('invalidAccountId'),
  category: z.string().optional(),
  startDate: z.string().datetime({ message: 'invalidDate' }),
  notes: z.string().optional(),
  familyId: z.string().uuid('invalidFamilyId'),
  installments: z.number().min(2, 'installmentsMustBeGreaterThanOne'),
});
