// App configuration and constants
export const config = {
  api: {
    baseUrl: import.meta.env.VITE_API_URL || 'http://localhost:3333',
    timeout: 10000,
  },
  app: {
    name: import.meta.env.VITE_APP_NAME || 'Granik',
    version: import.meta.env.VITE_APP_VERSION || '1.0.0',
  },
  auth: {
    tokenKey: import.meta.env.VITE_JWT_STORAGE_KEY || 'granik_auth_token',
    refreshThreshold: 5 * 60 * 1000, // 5 minutes before expiry
  },
  stripe: {
    publishableKey: import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '',
    enabled: import.meta.env.VITE_ENABLE_STRIPE === 'true',
  },
  features: {
    debug: import.meta.env.VITE_ENABLE_DEBUG === 'true',
  },
} as const;

export const routes = {
  home: '/',
  login: '/login',
  register: '/register',
  dashboard: '/dashboard',
  accounts: '/accounts',
  expenses: '/expenses',
  families: '/families',
  settings: '/settings',
  pricing: '/pricing',
} as const;

export const accountTypes = [
  { value: 'CHECKING', label: 'Checking' },
  { value: 'SAVINGS', label: 'Savings' },
  { value: 'CREDIT', label: 'Credit Card' },
  { value: 'INVESTMENT', label: 'Investment' },
  { value: 'LOAN', label: 'Loan' },
] as const;

export const expenseCategories = [
  { value: 'FOOD', label: 'Food & Dining' },
  { value: 'TRANSPORT', label: 'Transportation' },
  { value: 'SHOPPING', label: 'Shopping' },
  { value: 'ENTERTAINMENT', label: 'Entertainment' },
  { value: 'BILLS', label: 'Bills & Utilities' },
  { value: 'HEALTHCARE', label: 'Healthcare' },
  { value: 'EDUCATION', label: 'Education' },
  { value: 'TRAVEL', label: 'Travel' },
  { value: 'OTHER', label: 'Other' },
] as const;
