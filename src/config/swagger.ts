// src/setup/swagger.ts
import { Express } from 'express';
import swagger<PERSON>SDoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';
import { AccountSchemas, AuthSchemas, ExpenseSchemas } from '../docs/schemas';

export default function setupSwagger(app: Express) {
  const options: swaggerJSDoc.Options = {
    definition: {
      openapi: '3.0.0',
      info: {
        title: 'Granik API',
        version: '1.0.0',
        description: 'API for personal and family financial management',
      },
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      schemas: {
        ...AccountSchemas,
        ...AuthSchemas,
        ...ExpenseSchemas,
      },
      security: [{ bearerAuth: [] }],
    },
    apis: ['./src/config/routes.ts', './src/routers/*.ts'],
  };

  const swaggerDocs = swaggerJSDoc(options);
  app.get('/docs-json', (_, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(swaggerDocs);
  });
  app.use('/docs', swaggerUi.serve, swaggerUi.setup(swaggerDocs));
}
