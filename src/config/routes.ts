import { Express } from 'express';
import authRoutes from '../routers/authRoutes';
import expenseRoutes from '../routers/expenseRoutes';
import accountRoutes from '../routers/accountRoutes';
import { response } from '../utils/response';

export default function setupRoutes(app: Express) {
  app.get('/health', (req, res) => {
    response.success(req, res);
  });

  /**
   * @swagger
   * tags:
   *   name: Accounts
   *   description: Account management
   */
  app.use('/accounts', accountRoutes);

  /**
   * @swagger
   * tags:
   *   name: Auth
   *   description: Authentication and user profile
   */
  app.use('/auth', authRoutes);

  /**
   * @swagger
   * tags:
   *   name: Expenses
   *   description: Expense management
   */
  app.use('/expenses', expenseRoutes);
}
